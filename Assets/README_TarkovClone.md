# Escape from Tarkov Clone - Unity Project

This Unity project has been transformed into an Escape from Tarkov-style game with comprehensive systems for inventory management, loot spawning, AI enemies, weapon modification, and survival mechanics.

## 🎮 Features Implemented

### Core Game Systems
- **Raid Timer System**: 40-minute raid timer with automatic extraction when time runs out
- **Inventory System**: Grid-based inventory with weight management and secure container
- **Loot System**: Dynamic loot spawning with different tiers and respawn mechanics
- **Extraction System**: Multiple extraction points with different requirements and availability
- **AI Scav System**: Intelligent AI enemies with patrol, chase, and combat behaviors
- **Weapon Modification**: Modular weapon system with attachments affecting stats
- **Survival Mechanics**: Body part health system, bleeding, fractures, hunger, and thirst

### UI Systems
- **HUD**: Raid timer, health bars, weight indicator, crosshair
- **Inventory UI**: Grid-based inventory interface with drag-and-drop functionality
- **Extraction List**: Shows available extraction points and their status
- **Health System UI**: Body part health visualization and medical item usage

## 📁 Project Structure

```
Assets/
├── Scripts/
│   └── TarkovSystems/
│       ├── InventorySystem.cs          # Grid-based inventory management
│       ├── LootSystem.cs               # Dynamic loot spawning and management
│       ├── ExtractionSystem.cs         # Extraction points and zones
│       ├── AIScavSystem.cs             # AI enemy behavior and management
│       ├── WeaponModificationSystem.cs # Weapon attachments and modifications
│       ├── SurvivalSystem.cs           # Health, hunger, thirst mechanics
│       ├── TarkovUI.cs                 # User interface management
│       └── TarkovSceneSetup.cs         # Automated scene setup utility
├── Help.cs → TarkovGameManager.cs      # Main game manager (renamed and enhanced)
├── AlterunaFPS/                        # Existing FPS framework
├── Easy Weapons/                       # Existing weapon system
└── README_TarkovClone.md              # This file
```

## 🚀 Quick Setup

### Automatic Setup (Recommended)
1. Open your scene in Unity
2. Create an empty GameObject and attach the `TarkovSceneSetup` script
3. The script will automatically create all necessary systems and a sample map
4. Alternatively, right-click the `TarkovSceneSetup` component and select "Setup Tarkov Scene"

### Manual Setup
1. **Game Manager**: Add `TarkovGameManager` to a GameObject in your scene
2. **Inventory**: Add `InventorySystem` to a GameObject
3. **Loot**: Add `LootSystem` to a GameObject and configure loot spawn points
4. **Extraction**: Add `ExtractionSystem` to a GameObject and set up extraction zones
5. **AI**: Add `AIScavManager` to a GameObject and configure spawn points
6. **UI**: Add `TarkovUI` to a Canvas GameObject

## 🎯 Key Controls

- **Tab**: Toggle inventory
- **F**: Interact with loot/extraction points
- **H**: Use healing item (quick heal)
- **O**: Toggle extraction list
- **1-9**: Weapon switching (from Easy Weapons system)
- **Mouse Scroll**: Weapon switching (from Easy Weapons system)

## 🔧 System Details

### Inventory System
- **Grid Size**: 10x6 slots by default
- **Weight Limit**: 50kg maximum carrying capacity
- **Secure Container**: 2x2 protected storage
- **Item Types**: Weapons, Ammo, Medical, Food, Attachments, etc.

### Loot System
- **Loot Tiers**: Common, Uncommon, Rare, Epic, Legendary
- **Dynamic Spawning**: Configurable spawn chances and respawn timers
- **Weighted Selection**: Items spawn based on weight values

### AI Scav System
- **States**: Patrolling, Investigating, Chasing, Attacking
- **Detection**: Line-of-sight based player detection
- **Combat**: Raycast-based shooting with damage dealing
- **Loot Drops**: AI drops items when killed

### Survival System
- **Body Parts**: Head, Thorax, Stomach, Arms, Legs with individual health
- **Status Effects**: Bleeding, fractures, dehydration, exhaustion
- **Medical Items**: Bandages, medkits, painkillers, splints

### Weapon Modification
- **Attachment Types**: Scopes, barrels, grips, stocks, muzzles, etc.
- **Stat Modifiers**: Accuracy, recoil, range, ergonomics
- **Compatibility**: Attachment compatibility system

## 🛠️ Integration with Existing Systems

This Tarkov clone integrates with your existing Unity assets:

### AlterunaFPS Integration
- Uses existing player controller and networking capabilities
- Extends health system with body part damage
- Maintains multiplayer functionality

### Easy Weapons Integration
- Weapon modification system extends Easy Weapons
- Maintains existing weapon switching and firing mechanics
- Adds attachment system on top of base weapons

## 📋 TODO / Future Enhancements

- [ ] Complete UI implementation with proper drag-and-drop
- [ ] Add weapon attachment visual models
- [ ] Implement proper NavMesh AI pathfinding
- [ ] Add sound effects and visual feedback
- [ ] Create item database with ScriptableObjects
- [ ] Add more complex map generation
- [ ] Implement stash system for extracted items
- [ ] Add quest/task system
- [ ] Implement trader system
- [ ] Add weather and day/night cycle

## 🐛 Known Issues

1. **NavMesh Required**: AI Scavs require NavMesh to be baked manually
2. **UI Placeholders**: Some UI elements are basic and need visual polish
3. **Item Icons**: Items need proper icon sprites assigned
4. **Weapon Models**: Attachment visual models need to be created

## 🔧 Configuration

### Adjusting Raid Settings
Edit the `TarkovGameManager` component:
- `raidDuration`: Change raid time (default: 2400 seconds = 40 minutes)
- Player stats: Modify starting health, hydration, energy values

### Modifying Loot Spawns
Edit the `LootSystem` component:
- Add/remove loot spawn points
- Adjust loot tier probabilities
- Configure respawn timers

### AI Difficulty
Edit the `AIScav` component:
- `detectionRange`: How far AI can see players
- `damage`: Damage dealt by AI weapons
- `fireRate`: How fast AI shoots

## 📞 Support

This system provides a solid foundation for an Escape from Tarkov-style game. The modular design allows for easy expansion and customization of individual systems.

For best results:
1. Ensure NavMesh is baked for AI functionality
2. Assign proper item icons and weapon models
3. Configure loot tables and spawn points for your specific map
4. Test multiplayer functionality with AlterunaFPS systems

The code is well-documented and follows Unity best practices for easy modification and extension.
