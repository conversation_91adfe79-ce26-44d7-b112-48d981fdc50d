using UnityEngine;
using TarkovSystems;

public class TarkovGameManager : MonoBehaviour
{
    [Head<PERSON>("Game Settings")]
    public float raidDuration = 2400f; // 40 minutes in seconds
    public bool isInRaid = false;

    [Header("UI References")]
    public GameObject raidTimerUI;
    public GameObject extractionUI;
    public GameObject inventoryUI;

    [Header("System References")]
    public InventorySystem inventorySystem;
    public LootSystem lootSystem;
    public ExtractionSystem extractionSystem;

    [Header("Player Stats")]
    public float playerHealth = 100f;
    public float playerHydration = 100f;
    public float playerEnergy = 100f;
    public float playerStamina = 100f;

    private float currentRaidTime;
    private bool playerExtracted = false;

    public delegate void GameStateChangedHandler(bool inRaid);
    public event GameStateChangedHandler OnGameStateChanged;

    void Start()
    {
        InitializeRaid();
        InitializeSystems();
    }

    void Update()
    {
        if (isInRaid)
        {
            UpdateRaidTimer();
            UpdatePlayerStats();
            HandleInput();
        }
    }

    private void InitializeRaid()
    {
        currentRaidTime = raidDuration;
        isInRaid = true;
        playerExtracted = false;

        // Reset player stats
        playerHealth = 100f;
        playerHydration = 100f;
        playerEnergy = 100f;
        playerStamina = 100f;

        Debug.Log("Raid started! Duration: " + raidDuration + " seconds");
        OnGameStateChanged?.Invoke(true);
    }

    private void InitializeSystems()
    {
        // Find systems if not assigned
        if (inventorySystem == null)
            inventorySystem = FindObjectOfType<InventorySystem>();

        if (lootSystem == null)
            lootSystem = FindObjectOfType<LootSystem>();

        if (extractionSystem == null)
            extractionSystem = FindObjectOfType<ExtractionSystem>();
    }

    private void UpdateRaidTimer()
    {
        currentRaidTime -= Time.deltaTime;

        if (currentRaidTime <= 0)
        {
            EndRaid(false); // Time ran out
        }
    }

    private void UpdatePlayerStats()
    {
        // Gradual decrease of hydration and energy
        playerHydration -= Time.deltaTime * 0.1f; // Lose 0.1 per second
        playerEnergy -= Time.deltaTime * 0.05f;   // Lose 0.05 per second

        // Clamp values
        playerHydration = Mathf.Clamp(playerHydration, 0f, 100f);
        playerEnergy = Mathf.Clamp(playerEnergy, 0f, 100f);
        playerStamina = Mathf.Clamp(playerStamina, 0f, 100f);

        // Health effects from low hydration/energy
        if (playerHydration <= 0f || playerEnergy <= 0f)
        {
            playerHealth -= Time.deltaTime * 2f; // Lose 2 health per second
            playerHealth = Mathf.Clamp(playerHealth, 0f, 100f);

            if (playerHealth <= 0f)
            {
                PlayerDied();
            }
        }
    }

    private void HandleInput()
    {
        // Toggle inventory
        if (Input.GetKeyDown(KeyCode.Tab))
        {
            ToggleInventory();
        }

        // Quick heal (placeholder)
        if (Input.GetKeyDown(KeyCode.H))
        {
            UseHealingItem();
        }
    }

    private void ToggleInventory()
    {
        if (inventoryUI != null)
        {
            bool isActive = inventoryUI.activeSelf;
            inventoryUI.SetActive(!isActive);

            // Pause/unpause game when inventory is open
            Time.timeScale = isActive ? 1f : 0f;
            Cursor.lockState = isActive ? CursorLockMode.Locked : CursorLockMode.None;
        }
    }

    private void UseHealingItem()
    {
        // TODO: Check inventory for healing items and use them
        if (playerHealth < 100f)
        {
            playerHealth = Mathf.Min(playerHealth + 20f, 100f);
            Debug.Log("Used healing item. Health: " + playerHealth);
        }
    }

    public void PlayerExtracted(ExtractionPoint extractionPoint)
    {
        if (!isInRaid || playerExtracted) return;

        playerExtracted = true;
        Debug.Log($"Player extracted at {extractionPoint.extractionName}");
        EndRaid(true); // Successful extraction
    }

    private void PlayerDied()
    {
        Debug.Log("Player died!");
        EndRaid(false); // Death
    }

    private void EndRaid(bool successful)
    {
        isInRaid = false;

        if (successful)
        {
            Debug.Log("Raid completed successfully!");
            // TODO: Save extracted items to stash
        }
        else
        {
            Debug.Log("Raid failed!");
            // TODO: Lose items not in secure container
        }

        OnGameStateChanged?.Invoke(false);

        // TODO: Return to main menu or show raid results
    }

    public float GetRemainingTime()
    {
        return currentRaidTime;
    }

    public string GetFormattedTime()
    {
        int minutes = Mathf.FloorToInt(currentRaidTime / 60);
        int seconds = Mathf.FloorToInt(currentRaidTime % 60);
        return string.Format("{0:00}:{1:00}", minutes, seconds);
    }

    // Getters for UI
    public float GetPlayerHealth() => playerHealth;
    public float GetPlayerHydration() => playerHydration;
    public float GetPlayerEnergy() => playerEnergy;
    public float GetPlayerStamina() => playerStamina;

    // Setters for external systems
    public void TakeDamage(float damage)
    {
        playerHealth -= damage;
        playerHealth = Mathf.Clamp(playerHealth, 0f, 100f);

        if (playerHealth <= 0f)
        {
            PlayerDied();
        }
    }

    public void ConsumeFood(float energyRestore, float hydrationRestore)
    {
        playerEnergy = Mathf.Min(playerEnergy + energyRestore, 100f);
        playerHydration = Mathf.Min(playerHydration + hydrationRestore, 100f);
    }
}
