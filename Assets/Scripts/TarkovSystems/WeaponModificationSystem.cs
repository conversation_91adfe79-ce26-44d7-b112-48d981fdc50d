using System.Collections.Generic;
using UnityEngine;

namespace TarkovSystems
{
    public enum AttachmentType
    {
        Scope,
        Barrel,
        Grip,
        Stock,
        Muzzle,
        Magazine,
        Tactical,
        Handguard
    }
    
    [System.Serializable]
    public class WeaponAttachment : InventoryItem
    {
        public AttachmentType attachmentType;
        public float accuracyModifier = 0f;
        public float recoilModifier = 0f;
        public float rangeModifier = 0f;
        public float ergonomicsModifier = 0f;
        public GameObject attachmentPrefab;
        
        public WeaponAttachment(string id, string name, AttachmentType type) : base(id, name, ItemType.Attachment, 1, 1)
        {
            attachmentType = type;
        }
    }
    
    [System.Serializable]
    public class AttachmentSlot
    {
        public AttachmentType slotType;
        public Transform attachmentPoint;
        public WeaponAttachment currentAttachment;
        public bool isRequired = false;
        public List<string> compatibleAttachmentIds;
        
        public AttachmentSlot(AttachmentType type, Transform point)
        {
            slotType = type;
            attachmentPoint = point;
            compatibleAttachmentIds = new List<string>();
        }
    }
    
    public class ModifiableWeapon : MonoBehaviour
    {
        [Header("Weapon Base Stats")]
        public string weaponId;
        public string weaponName;
        public float baseAccuracy = 1f;
        public float baseRecoil = 1f;
        public float baseRange = 100f;
        public float baseErgonomics = 50f;
        public float baseDamage = 50f;
        
        [Header("Attachment Slots")]
        public List<AttachmentSlot> attachmentSlots;
        
        [Header("Visual")]
        public Transform weaponModel;
        
        private Dictionary<AttachmentType, AttachmentSlot> slotLookup;
        
        void Start()
        {
            InitializeSlots();
            UpdateWeaponStats();
        }
        
        private void InitializeSlots()
        {
            slotLookup = new Dictionary<AttachmentType, AttachmentSlot>();
            
            foreach (var slot in attachmentSlots)
            {
                slotLookup[slot.slotType] = slot;
            }
        }
        
        public bool CanAttachItem(WeaponAttachment attachment)
        {
            if (!slotLookup.ContainsKey(attachment.attachmentType))
                return false;
            
            var slot = slotLookup[attachment.attachmentType];
            
            // Check compatibility
            if (slot.compatibleAttachmentIds.Count > 0)
            {
                return slot.compatibleAttachmentIds.Contains(attachment.itemId);
            }
            
            return true;
        }
        
        public bool AttachItem(WeaponAttachment attachment)
        {
            if (!CanAttachItem(attachment))
                return false;
            
            var slot = slotLookup[attachment.attachmentType];
            
            // Remove existing attachment if any
            if (slot.currentAttachment != null)
            {
                DetachItem(attachment.attachmentType);
            }
            
            // Attach new item
            slot.currentAttachment = attachment;
            
            // Instantiate visual
            if (attachment.attachmentPrefab != null && slot.attachmentPoint != null)
            {
                GameObject attachmentObject = Instantiate(attachment.attachmentPrefab, slot.attachmentPoint);
                attachmentObject.transform.localPosition = Vector3.zero;
                attachmentObject.transform.localRotation = Quaternion.identity;
            }
            
            UpdateWeaponStats();
            Debug.Log($"Attached {attachment.itemName} to {weaponName}");
            return true;
        }
        
        public WeaponAttachment DetachItem(AttachmentType attachmentType)
        {
            if (!slotLookup.ContainsKey(attachmentType))
                return null;
            
            var slot = slotLookup[attachmentType];
            var detachedAttachment = slot.currentAttachment;
            
            if (detachedAttachment != null)
            {
                slot.currentAttachment = null;
                
                // Remove visual
                if (slot.attachmentPoint != null && slot.attachmentPoint.childCount > 0)
                {
                    for (int i = slot.attachmentPoint.childCount - 1; i >= 0; i--)
                    {
                        DestroyImmediate(slot.attachmentPoint.GetChild(i).gameObject);
                    }
                }
                
                UpdateWeaponStats();
                Debug.Log($"Detached {detachedAttachment.itemName} from {weaponName}");
            }
            
            return detachedAttachment;
        }
        
        private void UpdateWeaponStats()
        {
            // Calculate modified stats
            float totalAccuracy = baseAccuracy;
            float totalRecoil = baseRecoil;
            float totalRange = baseRange;
            float totalErgonomics = baseErgonomics;
            
            foreach (var slot in attachmentSlots)
            {
                if (slot.currentAttachment != null)
                {
                    totalAccuracy += slot.currentAttachment.accuracyModifier;
                    totalRecoil += slot.currentAttachment.recoilModifier;
                    totalRange += slot.currentAttachment.rangeModifier;
                    totalErgonomics += slot.currentAttachment.ergonomicsModifier;
                }
            }
            
            // Apply stats to weapon system
            var weaponComponent = GetComponent<Weapon>();
            if (weaponComponent != null)
            {
                // Modify Easy Weapons properties based on our stats
                weaponComponent.accuracy = Mathf.Clamp(totalAccuracy, 0.1f, 2f);
                // Note: Easy Weapons doesn't have direct recoil property, 
                // but we could modify other properties or create our own weapon system
            }
            
            Debug.Log($"Updated {weaponName} stats - Accuracy: {totalAccuracy:F2}, Recoil: {totalRecoil:F2}, Range: {totalRange:F2}, Ergonomics: {totalErgonomics:F2}");
        }
        
        public float GetCurrentAccuracy()
        {
            float total = baseAccuracy;
            foreach (var slot in attachmentSlots)
            {
                if (slot.currentAttachment != null)
                    total += slot.currentAttachment.accuracyModifier;
            }
            return total;
        }
        
        public float GetCurrentRecoil()
        {
            float total = baseRecoil;
            foreach (var slot in attachmentSlots)
            {
                if (slot.currentAttachment != null)
                    total += slot.currentAttachment.recoilModifier;
            }
            return total;
        }
        
        public float GetCurrentRange()
        {
            float total = baseRange;
            foreach (var slot in attachmentSlots)
            {
                if (slot.currentAttachment != null)
                    total += slot.currentAttachment.rangeModifier;
            }
            return total;
        }
        
        public float GetCurrentErgonomics()
        {
            float total = baseErgonomics;
            foreach (var slot in attachmentSlots)
            {
                if (slot.currentAttachment != null)
                    total += slot.currentAttachment.ergonomicsModifier;
            }
            return total;
        }
        
        public List<WeaponAttachment> GetAllAttachments()
        {
            var attachments = new List<WeaponAttachment>();
            foreach (var slot in attachmentSlots)
            {
                if (slot.currentAttachment != null)
                    attachments.Add(slot.currentAttachment);
            }
            return attachments;
        }
    }
    
    public class WeaponModificationUI : MonoBehaviour
    {
        [Header("UI References")]
        public GameObject modificationPanel;
        public Transform attachmentSlotsParent;
        public GameObject attachmentSlotPrefab;
        
        private ModifiableWeapon currentWeapon;
        private List<GameObject> slotUIElements;
        
        void Start()
        {
            if (modificationPanel != null)
                modificationPanel.SetActive(false);
                
            slotUIElements = new List<GameObject>();
        }
        
        public void OpenModificationUI(ModifiableWeapon weapon)
        {
            currentWeapon = weapon;
            
            if (modificationPanel != null)
            {
                modificationPanel.SetActive(true);
                CreateSlotUI();
            }
        }
        
        public void CloseModificationUI()
        {
            if (modificationPanel != null)
                modificationPanel.SetActive(false);
                
            ClearSlotUI();
            currentWeapon = null;
        }
        
        private void CreateSlotUI()
        {
            ClearSlotUI();
            
            if (currentWeapon == null || attachmentSlotsParent == null || attachmentSlotPrefab == null)
                return;
            
            foreach (var slot in currentWeapon.attachmentSlots)
            {
                GameObject slotUI = Instantiate(attachmentSlotPrefab, attachmentSlotsParent);
                slotUIElements.Add(slotUI);
                
                // Setup slot UI
                var slotText = slotUI.GetComponentInChildren<UnityEngine.UI.Text>();
                if (slotText != null)
                {
                    string slotName = slot.slotType.ToString();
                    string attachmentName = slot.currentAttachment != null ? slot.currentAttachment.itemName : "Empty";
                    slotText.text = $"{slotName}: {attachmentName}";
                }
                
                // Add click handler
                var button = slotUI.GetComponent<UnityEngine.UI.Button>();
                if (button != null)
                {
                    AttachmentType slotType = slot.slotType;
                    button.onClick.AddListener(() => OnSlotClicked(slotType));
                }
            }
        }
        
        private void ClearSlotUI()
        {
            foreach (var element in slotUIElements)
            {
                if (element != null)
                    Destroy(element);
            }
            slotUIElements.Clear();
        }
        
        private void OnSlotClicked(AttachmentType slotType)
        {
            if (currentWeapon == null) return;
            
            // TODO: Open attachment selection UI
            Debug.Log($"Clicked on {slotType} slot");
            
            // For now, just detach if something is attached
            var detached = currentWeapon.DetachItem(slotType);
            if (detached != null)
            {
                // Return to inventory
                var inventory = FindFirstObjectByType<InventorySystem>();
                if (inventory != null)
                {
                    inventory.TryAddItem(detached);
                }
            }
            
            // Refresh UI
            CreateSlotUI();
        }
    }
}
