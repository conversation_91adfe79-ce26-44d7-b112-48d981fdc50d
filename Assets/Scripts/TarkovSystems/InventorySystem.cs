using System.Collections.Generic;
using UnityEngine;

namespace TarkovSystems
{
    [System.Serializable]
    public class InventoryItem
    {
        public string itemId;
        public string itemName;
        public ItemType itemType;
        public int width;
        public int height;
        public int stackSize = 1;
        public int currentStack = 1;
        public Sprite icon;
        public GameObject prefab;
        public float weight;
        public int value;
        
        public InventoryItem(string id, string name, ItemType type, int w, int h)
        {
            itemId = id;
            itemName = name;
            itemType = type;
            width = w;
            height = h;
        }
    }

    public enum ItemType
    {
        Weapon,
        Ammo,
        Armor,
        Medical,
        Food,
        Attachment,
        Container,
        Key,
        Valuable,
        Barter
    }

    [System.Serializable]
    public class InventorySlot
    {
        public int x;
        public int y;
        public bool isOccupied;
        public InventoryItem item;
        
        public InventorySlot(int posX, int posY)
        {
            x = posX;
            y = posY;
            isOccupied = false;
            item = null;
        }
    }

    public class InventorySystem : MonoBehaviour
    {
        [Header("Inventory Settings")]
        public int inventoryWidth = 10;
        public int inventoryHeight = 6;
        public float maxWeight = 50f;
        
        [Header("Container Settings")]
        public int secureContainerWidth = 2;
        public int secureContainerHeight = 2;
        
        private InventorySlot[,] inventoryGrid;
        private InventorySlot[,] secureContainerGrid;
        private List<InventoryItem> allItems;
        private float currentWeight;
        
        public delegate void InventoryChangedHandler();
        public event InventoryChangedHandler OnInventoryChanged;
        
        void Start()
        {
            InitializeInventory();
            LoadItemDatabase();
        }
        
        private void InitializeInventory()
        {
            // Initialize main inventory grid
            inventoryGrid = new InventorySlot[inventoryWidth, inventoryHeight];
            for (int x = 0; x < inventoryWidth; x++)
            {
                for (int y = 0; y < inventoryHeight; y++)
                {
                    inventoryGrid[x, y] = new InventorySlot(x, y);
                }
            }
            
            // Initialize secure container grid
            secureContainerGrid = new InventorySlot[secureContainerWidth, secureContainerHeight];
            for (int x = 0; x < secureContainerWidth; x++)
            {
                for (int y = 0; y < secureContainerHeight; y++)
                {
                    secureContainerGrid[x, y] = new InventorySlot(x, y);
                }
            }
            
            allItems = new List<InventoryItem>();
            currentWeight = 0f;
        }
        
        private void LoadItemDatabase()
        {
            // TODO: Load from ScriptableObject database
            // For now, create some sample items
            CreateSampleItems();
        }
        
        private void CreateSampleItems()
        {
            // Sample weapons
            var ak74 = new InventoryItem("ak74", "AK-74", ItemType.Weapon, 4, 1);
            ak74.weight = 3.5f;
            ak74.value = 25000;
            
            var pistol = new InventoryItem("pm", "Makarov PM", ItemType.Weapon, 2, 1);
            pistol.weight = 0.8f;
            pistol.value = 5000;
            
            // Sample ammo
            var ammo545 = new InventoryItem("545x39", "5.45x39 PS", ItemType.Ammo, 1, 1);
            ammo545.stackSize = 60;
            ammo545.weight = 0.01f;
            ammo545.value = 150;
            
            // Sample medical
            var ifak = new InventoryItem("ifak", "IFAK", ItemType.Medical, 1, 2);
            ifak.weight = 0.2f;
            ifak.value = 8000;
            
            // Add to database (in real implementation, this would be loaded from files)
            allItems.AddRange(new[] { ak74, pistol, ammo545, ifak });
        }
        
        public bool TryAddItem(InventoryItem item, bool useSecureContainer = false)
        {
            var targetGrid = useSecureContainer ? secureContainerGrid : inventoryGrid;
            var targetWidth = useSecureContainer ? secureContainerWidth : inventoryWidth;
            var targetHeight = useSecureContainer ? secureContainerHeight : inventoryHeight;
            
            // Check weight limit
            if (currentWeight + item.weight > maxWeight)
            {
                Debug.Log("Cannot add item: Weight limit exceeded");
                return false;
            }
            
            // Try to find a suitable position
            for (int x = 0; x <= targetWidth - item.width; x++)
            {
                for (int y = 0; y <= targetHeight - item.height; y++)
                {
                    if (CanPlaceItemAt(x, y, item, targetGrid))
                    {
                        PlaceItemAt(x, y, item, targetGrid);
                        currentWeight += item.weight;
                        OnInventoryChanged?.Invoke();
                        return true;
                    }
                }
            }
            
            Debug.Log("Cannot add item: No space available");
            return false;
        }
        
        private bool CanPlaceItemAt(int startX, int startY, InventoryItem item, InventorySlot[,] grid)
        {
            for (int x = startX; x < startX + item.width; x++)
            {
                for (int y = startY; y < startY + item.height; y++)
                {
                    if (grid[x, y].isOccupied)
                        return false;
                }
            }
            return true;
        }
        
        private void PlaceItemAt(int startX, int startY, InventoryItem item, InventorySlot[,] grid)
        {
            for (int x = startX; x < startX + item.width; x++)
            {
                for (int y = startY; y < startY + item.height; y++)
                {
                    grid[x, y].isOccupied = true;
                    grid[x, y].item = item;
                }
            }
        }
        
        public bool RemoveItem(int x, int y, bool fromSecureContainer = false)
        {
            var targetGrid = fromSecureContainer ? secureContainerGrid : inventoryGrid;
            
            if (targetGrid[x, y].isOccupied)
            {
                var item = targetGrid[x, y].item;
                
                // Clear all slots occupied by this item
                for (int i = 0; i < (fromSecureContainer ? secureContainerWidth : inventoryWidth); i++)
                {
                    for (int j = 0; j < (fromSecureContainer ? secureContainerHeight : inventoryHeight); j++)
                    {
                        if (targetGrid[i, j].item == item)
                        {
                            targetGrid[i, j].isOccupied = false;
                            targetGrid[i, j].item = null;
                        }
                    }
                }
                
                currentWeight -= item.weight;
                OnInventoryChanged?.Invoke();
                return true;
            }
            
            return false;
        }
        
        public InventoryItem GetItemAt(int x, int y, bool fromSecureContainer = false)
        {
            var targetGrid = fromSecureContainer ? secureContainerGrid : inventoryGrid;
            return targetGrid[x, y].item;
        }
        
        public float GetCurrentWeight()
        {
            return currentWeight;
        }
        
        public float GetWeightPercentage()
        {
            return currentWeight / maxWeight;
        }
        
        public List<InventoryItem> GetAllItems()
        {
            return new List<InventoryItem>(allItems);
        }
        
        public InventoryItem GetItemById(string itemId)
        {
            return allItems.Find(item => item.itemId == itemId);
        }
    }
}
