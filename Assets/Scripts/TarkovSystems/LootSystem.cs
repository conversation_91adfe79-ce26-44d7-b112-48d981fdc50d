using System.Collections.Generic;
using UnityEngine;

namespace TarkovSystems
{
    [System.Serializable]
    public class LootSpawnPoint
    {
        public Transform spawnTransform;
        public LootTier lootTier;
        public float spawnChance = 0.5f;
        public bool hasSpawned = false;
        public GameObject currentLoot;
        
        public LootSpawnPoint(Transform transform, LootTier tier)
        {
            spawnTransform = transform;
            lootTier = tier;
        }
    }
    
    public enum LootTier
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary
    }
    
    [System.Serializable]
    public class LootTable
    {
        public LootTier tier;
        public List<LootEntry> lootEntries;
        
        public LootTable(LootTier lootTier)
        {
            tier = lootTier;
            lootEntries = new List<LootEntry>();
        }
    }
    
    [System.Serializable]
    public class LootEntry
    {
        public InventoryItem item;
        public float spawnWeight;
        public int minQuantity = 1;
        public int maxQuantity = 1;
        
        public LootEntry(InventoryItem lootItem, float weight)
        {
            item = lootItem;
            spawnWeight = weight;
        }
    }

    public class LootableObject : MonoBehaviour
    {
        [Header("Loot Settings")]
        public List<InventoryItem> lootItems;
        public bool isLooted = false;
        public float interactionRange = 2f;
        
        [Header("UI")]
        public GameObject lootPrompt;
        
        private bool playerInRange = false;
        
        void Start()
        {
            if (lootPrompt != null)
                lootPrompt.SetActive(false);
        }
        
        void Update()
        {
            if (playerInRange && !isLooted && Input.GetKeyDown(KeyCode.F))
            {
                LootObject();
            }
        }
        
        private void OnTriggerEnter(Collider other)
        {
            if (other.CompareTag("Player"))
            {
                playerInRange = true;
                if (lootPrompt != null && !isLooted)
                    lootPrompt.SetActive(true);
            }
        }
        
        private void OnTriggerExit(Collider other)
        {
            if (other.CompareTag("Player"))
            {
                playerInRange = false;
                if (lootPrompt != null)
                    lootPrompt.SetActive(false);
            }
        }
        
        private void LootObject()
        {
            var playerInventory = FindObjectOfType<InventorySystem>();
            if (playerInventory != null)
            {
                foreach (var item in lootItems)
                {
                    if (playerInventory.TryAddItem(item))
                    {
                        Debug.Log($"Looted: {item.itemName}");
                    }
                    else
                    {
                        Debug.Log($"Cannot loot {item.itemName}: Inventory full");
                        // TODO: Drop item on ground
                    }
                }
                
                isLooted = true;
                if (lootPrompt != null)
                    lootPrompt.SetActive(false);
                
                // Visual feedback
                GetComponent<Renderer>().material.color = Color.gray;
            }
        }
    }

    public class LootSystem : MonoBehaviour
    {
        [Header("Loot Settings")]
        public List<LootSpawnPoint> lootSpawnPoints;
        public List<LootTable> lootTables;
        public GameObject[] lootPrefabs;
        
        [Header("Dynamic Loot")]
        public bool enableDynamicLoot = true;
        public float lootRespawnTime = 300f; // 5 minutes
        
        private InventorySystem inventorySystem;
        
        void Start()
        {
            inventorySystem = FindObjectOfType<InventorySystem>();
            InitializeLootTables();
            SpawnInitialLoot();
            
            if (enableDynamicLoot)
            {
                InvokeRepeating(nameof(RespawnLoot), lootRespawnTime, lootRespawnTime);
            }
        }
        
        private void InitializeLootTables()
        {
            lootTables = new List<LootTable>();
            
            // Common loot table
            var commonTable = new LootTable(LootTier.Common);
            if (inventorySystem != null)
            {
                var allItems = inventorySystem.GetAllItems();
                foreach (var item in allItems)
                {
                    if (item.itemType == ItemType.Ammo || item.itemType == ItemType.Food)
                    {
                        commonTable.lootEntries.Add(new LootEntry(item, 10f));
                    }
                }
            }
            lootTables.Add(commonTable);
            
            // Uncommon loot table
            var uncommonTable = new LootTable(LootTier.Uncommon);
            if (inventorySystem != null)
            {
                var allItems = inventorySystem.GetAllItems();
                foreach (var item in allItems)
                {
                    if (item.itemType == ItemType.Medical || item.itemType == ItemType.Attachment)
                    {
                        uncommonTable.lootEntries.Add(new LootEntry(item, 5f));
                    }
                }
            }
            lootTables.Add(uncommonTable);
            
            // Rare loot table
            var rareTable = new LootTable(LootTier.Rare);
            if (inventorySystem != null)
            {
                var allItems = inventorySystem.GetAllItems();
                foreach (var item in allItems)
                {
                    if (item.itemType == ItemType.Weapon || item.itemType == ItemType.Armor)
                    {
                        rareTable.lootEntries.Add(new LootEntry(item, 2f));
                    }
                }
            }
            lootTables.Add(rareTable);
        }
        
        private void SpawnInitialLoot()
        {
            foreach (var spawnPoint in lootSpawnPoints)
            {
                if (Random.value <= spawnPoint.spawnChance)
                {
                    SpawnLootAtPoint(spawnPoint);
                }
            }
        }
        
        private void SpawnLootAtPoint(LootSpawnPoint spawnPoint)
        {
            if (spawnPoint.hasSpawned) return;
            
            var lootTable = GetLootTableByTier(spawnPoint.lootTier);
            if (lootTable != null && lootTable.lootEntries.Count > 0)
            {
                var selectedItem = SelectRandomLoot(lootTable);
                if (selectedItem != null)
                {
                    // Create loot object
                    GameObject lootObject = new GameObject($"Loot_{selectedItem.itemName}");
                    lootObject.transform.position = spawnPoint.spawnTransform.position;
                    lootObject.transform.rotation = spawnPoint.spawnTransform.rotation;
                    
                    // Add components
                    var lootableComponent = lootObject.AddComponent<LootableObject>();
                    lootableComponent.lootItems = new List<InventoryItem> { selectedItem };
                    
                    // Add visual representation
                    if (selectedItem.prefab != null)
                    {
                        var visual = Instantiate(selectedItem.prefab, lootObject.transform);
                        visual.transform.localPosition = Vector3.zero;
                    }
                    else
                    {
                        // Default cube representation
                        var cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
                        cube.transform.SetParent(lootObject.transform);
                        cube.transform.localPosition = Vector3.zero;
                        cube.transform.localScale = Vector3.one * 0.5f;
                    }
                    
                    // Add collider for interaction
                    var collider = lootObject.AddComponent<BoxCollider>();
                    collider.isTrigger = true;
                    collider.size = Vector3.one * 2f;
                    
                    spawnPoint.currentLoot = lootObject;
                    spawnPoint.hasSpawned = true;
                }
            }
        }
        
        private LootTable GetLootTableByTier(LootTier tier)
        {
            return lootTables.Find(table => table.tier == tier);
        }
        
        private InventoryItem SelectRandomLoot(LootTable lootTable)
        {
            float totalWeight = 0f;
            foreach (var entry in lootTable.lootEntries)
            {
                totalWeight += entry.spawnWeight;
            }
            
            float randomValue = Random.value * totalWeight;
            float currentWeight = 0f;
            
            foreach (var entry in lootTable.lootEntries)
            {
                currentWeight += entry.spawnWeight;
                if (randomValue <= currentWeight)
                {
                    return entry.item;
                }
            }
            
            return lootTable.lootEntries[0].item; // Fallback
        }
        
        private void RespawnLoot()
        {
            foreach (var spawnPoint in lootSpawnPoints)
            {
                if (spawnPoint.hasSpawned && spawnPoint.currentLoot == null)
                {
                    spawnPoint.hasSpawned = false;
                    if (Random.value <= spawnPoint.spawnChance)
                    {
                        SpawnLootAtPoint(spawnPoint);
                    }
                }
            }
        }
        
        public void AddLootSpawnPoint(Transform transform, LootTier tier, float spawnChance = 0.5f)
        {
            var newSpawnPoint = new LootSpawnPoint(transform, tier);
            newSpawnPoint.spawnChance = spawnChance;
            lootSpawnPoints.Add(newSpawnPoint);
        }
    }
}
