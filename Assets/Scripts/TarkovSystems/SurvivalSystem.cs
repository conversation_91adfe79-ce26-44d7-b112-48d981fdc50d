using UnityEngine;
using System.Collections.Generic;

namespace TarkovSystems
{
    public enum BodyPart
    {
        Head,
        Thorax,
        Stomach,
        LeftArm,
        RightArm,
        LeftLeg,
        RightLeg
    }
    
    public enum HealthStatus
    {
        Healthy,
        LightlyWounded,
        HeavilyWounded,
        Blacked,
        Fractured
    }
    
    [System.Serializable]
    public class BodyPartHealth
    {
        public BodyPart bodyPart;
        public float maxHealth;
        public float currentHealth;
        public HealthStatus status;
        public bool isFractured;
        public bool isBleeding;
        public float bleedingRate;
        
        public BodyPartHealth(BodyPart part, float maxHp)
        {
            bodyPart = part;
            maxHealth = maxHp;
            currentHealth = maxHp;
            status = HealthStatus.Healthy;
            isFractured = false;
            isBleeding = false;
            bleedingRate = 0f;
        }
        
        public void TakeDamage(float damage)
        {
            currentHealth -= damage;
            currentHealth = Mathf.Clamp(currentHealth, 0f, maxHealth);
            
            UpdateStatus();
            
            // Chance of bleeding
            if (damage > 10f && Random.value < 0.3f)
            {
                StartBleeding(damage * 0.1f);
            }
            
            // Chance of fracture for limbs
            if ((bodyPart == BodyPart.LeftArm || bodyPart == BodyPart.RightArm || 
                 bodyPart == BodyPart.LeftLeg || bodyPart == BodyPart.RightLeg) && 
                damage > 20f && Random.value < 0.2f)
            {
                isFractured = true;
            }
        }
        
        public void Heal(float healAmount)
        {
            if (status != HealthStatus.Blacked)
            {
                currentHealth += healAmount;
                currentHealth = Mathf.Clamp(currentHealth, 0f, maxHealth);
                UpdateStatus();
            }
        }
        
        public void StartBleeding(float rate)
        {
            isBleeding = true;
            bleedingRate = rate;
        }
        
        public void StopBleeding()
        {
            isBleeding = false;
            bleedingRate = 0f;
        }
        
        private void UpdateStatus()
        {
            float healthPercentage = currentHealth / maxHealth;
            
            if (currentHealth <= 0)
            {
                status = HealthStatus.Blacked;
            }
            else if (healthPercentage < 0.3f)
            {
                status = HealthStatus.HeavilyWounded;
            }
            else if (healthPercentage < 0.7f)
            {
                status = HealthStatus.LightlyWounded;
            }
            else
            {
                status = HealthStatus.Healthy;
            }
        }
    }
    
    public enum MedicalItemType
    {
        Bandage,
        Medkit,
        Painkiller,
        Splint,
        Surgery,
        Stimulant
    }
    
    [System.Serializable]
    public class MedicalItem : InventoryItem
    {
        public MedicalItemType medicalType;
        public float healAmount;
        public float useTime;
        public bool stopsBleeding;
        public bool fixesFractures;
        public bool canHealBlackedLimbs;
        public int uses;
        public int maxUses;
        
        public MedicalItem(string id, string name, MedicalItemType type) : base(id, name, ItemType.Medical, 1, 1)
        {
            medicalType = type;
            uses = maxUses;
        }
    }

    public class SurvivalSystem : MonoBehaviour
    {
        [Header("Body Parts")]
        public List<BodyPartHealth> bodyParts;
        
        [Header("Survival Stats")]
        public float hydration = 100f;
        public float energy = 100f;
        public float temperature = 36.5f; // Celsius
        
        [Header("Effects")]
        public bool isPainKilled = false;
        public float painKillerDuration = 0f;
        public bool isDehydrated = false;
        public bool isExhausted = false;
        
        [Header("Rates")]
        public float hydrationDecayRate = 0.1f;
        public float energyDecayRate = 0.05f;
        public float bleedingDamageMultiplier = 1f;
        
        private TarkovGameManager gameManager;
        
        public delegate void HealthChangedHandler(BodyPart bodyPart, float newHealth);
        public event HealthChangedHandler OnHealthChanged;
        
        public delegate void SurvivalStatChangedHandler(float hydration, float energy);
        public event SurvivalStatChangedHandler OnSurvivalStatChanged;
        
        void Start()
        {
            gameManager = FindFirstObjectByType<TarkovGameManager>();
            InitializeBodyParts();
        }
        
        void Update()
        {
            UpdateSurvivalStats();
            UpdateBleeding();
            UpdatePainKillers();
            CheckSurvivalEffects();
        }
        
        private void InitializeBodyParts()
        {
            if (bodyParts == null || bodyParts.Count == 0)
            {
                bodyParts = new List<BodyPartHealth>
                {
                    new BodyPartHealth(BodyPart.Head, 35f),
                    new BodyPartHealth(BodyPart.Thorax, 85f),
                    new BodyPartHealth(BodyPart.Stomach, 70f),
                    new BodyPartHealth(BodyPart.LeftArm, 60f),
                    new BodyPartHealth(BodyPart.RightArm, 60f),
                    new BodyPartHealth(BodyPart.LeftLeg, 65f),
                    new BodyPartHealth(BodyPart.RightLeg, 65f)
                };
            }
        }
        
        private void UpdateSurvivalStats()
        {
            // Decrease hydration and energy over time
            hydration -= hydrationDecayRate * Time.deltaTime;
            energy -= energyDecayRate * Time.deltaTime;
            
            hydration = Mathf.Clamp(hydration, 0f, 100f);
            energy = Mathf.Clamp(energy, 0f, 100f);
            
            OnSurvivalStatChanged?.Invoke(hydration, energy);
        }
        
        private void UpdateBleeding()
        {
            foreach (var bodyPart in bodyParts)
            {
                if (bodyPart.isBleeding)
                {
                    float bleedDamage = bodyPart.bleedingRate * bleedingDamageMultiplier * Time.deltaTime;
                    bodyPart.currentHealth -= bleedDamage;
                    bodyPart.currentHealth = Mathf.Clamp(bodyPart.currentHealth, 0f, bodyPart.maxHealth);
                    
                    OnHealthChanged?.Invoke(bodyPart.bodyPart, bodyPart.currentHealth);
                    
                    // Check if body part is now blacked
                    if (bodyPart.currentHealth <= 0)
                    {
                        bodyPart.status = HealthStatus.Blacked;
                        bodyPart.StopBleeding(); // Stop bleeding when blacked
                        
                        // Critical body parts cause death
                        if (bodyPart.bodyPart == BodyPart.Head || bodyPart.bodyPart == BodyPart.Thorax)
                        {
                            Die();
                        }
                    }
                }
            }
        }
        
        private void UpdatePainKillers()
        {
            if (isPainKilled)
            {
                painKillerDuration -= Time.deltaTime;
                if (painKillerDuration <= 0)
                {
                    isPainKilled = false;
                }
            }
        }
        
        private void CheckSurvivalEffects()
        {
            // Dehydration effects
            isDehydrated = hydration <= 20f;
            if (hydration <= 0f)
            {
                // Take damage from dehydration
                TakeDamage(BodyPart.Thorax, 2f * Time.deltaTime);
            }
            
            // Exhaustion effects
            isExhausted = energy <= 20f;
            if (energy <= 0f)
            {
                // Take damage from exhaustion
                TakeDamage(BodyPart.Thorax, 1f * Time.deltaTime);
            }
        }
        
        public void TakeDamage(BodyPart targetBodyPart, float damage)
        {
            var bodyPart = GetBodyPart(targetBodyPart);
            if (bodyPart != null)
            {
                bodyPart.TakeDamage(damage);
                OnHealthChanged?.Invoke(targetBodyPart, bodyPart.currentHealth);
                
                // Check for death
                if ((targetBodyPart == BodyPart.Head || targetBodyPart == BodyPart.Thorax) && 
                    bodyPart.currentHealth <= 0)
                {
                    Die();
                }
            }
        }
        
        public bool UseMedicalItem(MedicalItem medItem, BodyPart targetBodyPart)
        {
            if (medItem.uses <= 0) return false;
            
            var bodyPart = GetBodyPart(targetBodyPart);
            if (bodyPart == null) return false;
            
            // Check if item can be used on this body part
            if (bodyPart.status == HealthStatus.Blacked && !medItem.canHealBlackedLimbs)
            {
                Debug.Log("Cannot heal blacked body part with this item");
                return false;
            }
            
            // Apply healing
            if (medItem.healAmount > 0)
            {
                bodyPart.Heal(medItem.healAmount);
            }
            
            // Stop bleeding
            if (medItem.stopsBleeding && bodyPart.isBleeding)
            {
                bodyPart.StopBleeding();
            }
            
            // Fix fractures
            if (medItem.fixesFractures && bodyPart.isFractured)
            {
                bodyPart.isFractured = false;
            }
            
            // Painkiller effects
            if (medItem.medicalType == MedicalItemType.Painkiller)
            {
                isPainKilled = true;
                painKillerDuration = 300f; // 5 minutes
            }
            
            // Use up the item
            medItem.uses--;
            
            OnHealthChanged?.Invoke(targetBodyPart, bodyPart.currentHealth);
            Debug.Log($"Used {medItem.itemName} on {targetBodyPart}");
            
            return true;
        }
        
        public void ConsumeFood(float energyRestore, float hydrationRestore)
        {
            energy = Mathf.Min(energy + energyRestore, 100f);
            hydration = Mathf.Min(hydration + hydrationRestore, 100f);
            
            OnSurvivalStatChanged?.Invoke(hydration, energy);
        }
        
        public float GetTotalHealth()
        {
            float total = 0f;
            float max = 0f;
            
            foreach (var bodyPart in bodyParts)
            {
                total += bodyPart.currentHealth;
                max += bodyPart.maxHealth;
            }
            
            return (total / max) * 100f;
        }
        
        public bool IsAlive()
        {
            var head = GetBodyPart(BodyPart.Head);
            var thorax = GetBodyPart(BodyPart.Thorax);
            
            return head.currentHealth > 0 && thorax.currentHealth > 0;
        }
        
        public BodyPartHealth GetBodyPart(BodyPart bodyPart)
        {
            return bodyParts.Find(bp => bp.bodyPart == bodyPart);
        }
        
        public List<BodyPartHealth> GetDamagedBodyParts()
        {
            return bodyParts.FindAll(bp => bp.status != HealthStatus.Healthy);
        }
        
        public bool HasBleeding()
        {
            return bodyParts.Exists(bp => bp.isBleeding);
        }
        
        public bool HasFractures()
        {
            return bodyParts.Exists(bp => bp.isFractured);
        }
        
        private void Die()
        {
            Debug.Log("Player died!");
            
            if (gameManager != null)
            {
                // This would trigger the game manager's death handling
                gameManager.TakeDamage(1000f); // Ensure death
            }
        }
        
        // Quick heal method for testing
        public void FullHeal()
        {
            foreach (var bodyPart in bodyParts)
            {
                bodyPart.currentHealth = bodyPart.maxHealth;
                bodyPart.status = HealthStatus.Healthy;
                bodyPart.isBleeding = false;
                bodyPart.isFractured = false;
                bodyPart.bleedingRate = 0f;
                
                OnHealthChanged?.Invoke(bodyPart.bodyPart, bodyPart.currentHealth);
            }
            
            hydration = 100f;
            energy = 100f;
            OnSurvivalStatChanged?.Invoke(hydration, energy);
        }
    }
}
