using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;

namespace TarkovSystems
{
    public enum ScavState
    {
        Patrolling,
        Investigating,
        Chasing,
        Attacking,
        Dead
    }
    
    public class AIScav : MonoBehaviour
    {
        [Header("AI Settings")]
        public float detectionRange = 15f;
        public float attackRange = 8f;
        public float patrolRadius = 20f;
        public float moveSpeed = 3.5f;
        public float runSpeed = 6f;
        
        [Header("Combat")]
        public float health = 100f;
        public float damage = 25f;
        public float fireRate = 0.5f;
        public GameObject weaponPrefab;
        
        [Header("Loot")]
        public List<InventoryItem> lootTable;
        public int minLootItems = 1;
        public int maxLootItems = 3;
        
        [Header("Patrol Points")]
        public Transform[] patrolPoints;
        
        private NavMeshAgent agent;
        private ScavState currentState;
        private Transform player;
        private Vector3 originalPosition;
        private int currentPatrolIndex = 0;
        private float lastFireTime;
        private bool isDead = false;
        
        // Investigation
        private Vector3 investigationPoint;
        private float investigationTime = 5f;
        private float investigationTimer;
        
        void Start()
        {
            agent = GetComponent<NavMeshAgent>();
            if (agent == null)
            {
                agent = gameObject.AddComponent<NavMeshAgent>();
            }
            
            originalPosition = transform.position;
            currentState = ScavState.Patrolling;
            agent.speed = moveSpeed;
            
            // Find player
            GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
            if (playerObj != null)
            {
                player = playerObj.transform;
            }
            
            // Setup patrol points if none assigned
            if (patrolPoints == null || patrolPoints.Length == 0)
            {
                CreateDefaultPatrolPoints();
            }
            
            StartPatrol();
        }
        
        void Update()
        {
            if (isDead) return;
            
            switch (currentState)
            {
                case ScavState.Patrolling:
                    HandlePatrolling();
                    break;
                case ScavState.Investigating:
                    HandleInvestigating();
                    break;
                case ScavState.Chasing:
                    HandleChasing();
                    break;
                case ScavState.Attacking:
                    HandleAttacking();
                    break;
            }
            
            CheckForPlayer();
        }
        
        private void HandlePatrolling()
        {
            if (!agent.pathPending && agent.remainingDistance < 0.5f)
            {
                // Move to next patrol point
                currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
                agent.SetDestination(patrolPoints[currentPatrolIndex].position);
            }
        }
        
        private void HandleInvestigating()
        {
            investigationTimer -= Time.deltaTime;
            
            if (!agent.pathPending && agent.remainingDistance < 1f)
            {
                // Look around
                transform.Rotate(0, 45f * Time.deltaTime, 0);
            }
            
            if (investigationTimer <= 0)
            {
                // Return to patrol
                ChangeState(ScavState.Patrolling);
                StartPatrol();
            }
        }
        
        private void HandleChasing()
        {
            if (player != null)
            {
                float distanceToPlayer = Vector3.Distance(transform.position, player.position);
                
                if (distanceToPlayer <= attackRange)
                {
                    ChangeState(ScavState.Attacking);
                }
                else if (distanceToPlayer > detectionRange * 1.5f)
                {
                    // Lost player, investigate last known position
                    investigationPoint = player.position;
                    ChangeState(ScavState.Investigating);
                    agent.SetDestination(investigationPoint);
                    investigationTimer = investigationTime;
                }
                else
                {
                    // Continue chasing
                    agent.SetDestination(player.position);
                }
            }
        }
        
        private void HandleAttacking()
        {
            if (player != null)
            {
                float distanceToPlayer = Vector3.Distance(transform.position, player.position);
                
                if (distanceToPlayer > attackRange)
                {
                    ChangeState(ScavState.Chasing);
                    return;
                }
                
                // Face the player
                Vector3 direction = (player.position - transform.position).normalized;
                transform.rotation = Quaternion.LookRotation(direction);
                
                // Fire weapon
                if (Time.time - lastFireTime >= fireRate)
                {
                    FireWeapon();
                    lastFireTime = Time.time;
                }
            }
        }
        
        private void CheckForPlayer()
        {
            if (player == null || currentState == ScavState.Dead) return;
            
            float distanceToPlayer = Vector3.Distance(transform.position, player.position);
            
            if (distanceToPlayer <= detectionRange && currentState == ScavState.Patrolling)
            {
                // Check line of sight
                RaycastHit hit;
                Vector3 directionToPlayer = (player.position - transform.position).normalized;
                
                if (Physics.Raycast(transform.position + Vector3.up, directionToPlayer, out hit, detectionRange))
                {
                    if (hit.collider.CompareTag("Player"))
                    {
                        // Player detected!
                        ChangeState(ScavState.Chasing);
                        Debug.Log("Scav detected player!");
                    }
                }
            }
        }
        
        private void FireWeapon()
        {
            if (player == null) return;
            
            // Simple raycast weapon
            RaycastHit hit;
            Vector3 fireDirection = (player.position - transform.position).normalized;
            
            if (Physics.Raycast(transform.position + Vector3.up, fireDirection, out hit, attackRange))
            {
                if (hit.collider.CompareTag("Player"))
                {
                    // Deal damage to player
                    var gameManager = FindFirstObjectByType<TarkovGameManager>();
                    if (gameManager != null)
                    {
                        gameManager.TakeDamage(damage);
                        Debug.Log($"Scav hit player for {damage} damage!");
                    }
                }
            }
            
            // Visual/audio feedback would go here
            Debug.Log("Scav fired weapon!");
        }
        
        private void ChangeState(ScavState newState)
        {
            currentState = newState;
            
            switch (newState)
            {
                case ScavState.Patrolling:
                    agent.speed = moveSpeed;
                    break;
                case ScavState.Chasing:
                case ScavState.Attacking:
                    agent.speed = runSpeed;
                    break;
                case ScavState.Investigating:
                    agent.speed = moveSpeed;
                    break;
            }
        }
        
        private void StartPatrol()
        {
            if (patrolPoints.Length > 0)
            {
                agent.SetDestination(patrolPoints[currentPatrolIndex].position);
            }
        }
        
        private void CreateDefaultPatrolPoints()
        {
            // Create 4 patrol points in a square around the original position
            patrolPoints = new Transform[4];
            
            for (int i = 0; i < 4; i++)
            {
                GameObject patrolPoint = new GameObject($"PatrolPoint_{i}");
                patrolPoint.transform.SetParent(transform);
                
                float angle = i * 90f * Mathf.Deg2Rad;
                Vector3 offset = new Vector3(Mathf.Cos(angle), 0, Mathf.Sin(angle)) * patrolRadius;
                patrolPoint.transform.position = originalPosition + offset;
                
                patrolPoints[i] = patrolPoint.transform;
            }
        }
        
        public void TakeDamage(float damageAmount)
        {
            if (isDead) return;
            
            health -= damageAmount;
            
            if (health <= 0)
            {
                Die();
            }
            else
            {
                // React to damage - investigate or chase
                if (currentState == ScavState.Patrolling)
                {
                    if (player != null)
                    {
                        ChangeState(ScavState.Chasing);
                    }
                    else
                    {
                        investigationPoint = transform.position;
                        ChangeState(ScavState.Investigating);
                        investigationTimer = investigationTime;
                    }
                }
            }
        }
        
        private void Die()
        {
            isDead = true;
            currentState = ScavState.Dead;
            agent.enabled = false;
            
            // Drop loot
            DropLoot();
            
            // Disable AI behavior
            this.enabled = false;
            
            Debug.Log("Scav died!");
        }
        
        private void DropLoot()
        {
            if (lootTable.Count == 0) return;
            
            int lootCount = Random.Range(minLootItems, maxLootItems + 1);
            
            for (int i = 0; i < lootCount; i++)
            {
                if (lootTable.Count > 0)
                {
                    var randomItem = lootTable[Random.Range(0, lootTable.Count)];
                    
                    // Create loot object
                    GameObject lootObject = new GameObject($"ScavLoot_{randomItem.itemName}");
                    lootObject.transform.position = transform.position + Random.insideUnitSphere * 2f;
                    lootObject.transform.position = new Vector3(lootObject.transform.position.x, transform.position.y, lootObject.transform.position.z);
                    
                    var lootableComponent = lootObject.AddComponent<LootableObject>();
                    lootableComponent.lootItems = new List<InventoryItem> { randomItem };
                    
                    // Add visual
                    var cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
                    cube.transform.SetParent(lootObject.transform);
                    cube.transform.localPosition = Vector3.zero;
                    cube.transform.localScale = Vector3.one * 0.3f;
                    
                    // Add collider
                    var collider = lootObject.AddComponent<BoxCollider>();
                    collider.isTrigger = true;
                }
            }
        }
    }
    
    public class AIScavManager : MonoBehaviour
    {
        [Header("Spawn Settings")]
        public GameObject scavPrefab;
        public Transform[] spawnPoints;
        public int maxScavs = 10;
        public float spawnDelay = 30f;
        
        private List<AIScav> activeScavs;
        
        void Start()
        {
            activeScavs = new List<AIScav>();
            SpawnInitialScavs();
            InvokeRepeating(nameof(CheckScavCount), spawnDelay, spawnDelay);
        }
        
        private void SpawnInitialScavs()
        {
            int initialScavs = Mathf.Min(maxScavs / 2, spawnPoints.Length);
            
            for (int i = 0; i < initialScavs; i++)
            {
                SpawnScav(spawnPoints[i]);
            }
        }
        
        private void CheckScavCount()
        {
            // Remove dead scavs from list
            activeScavs.RemoveAll(scav => scav == null || scav.currentState == ScavState.Dead);
            
            // Spawn new scavs if below max
            if (activeScavs.Count < maxScavs && spawnPoints.Length > 0)
            {
                var randomSpawnPoint = spawnPoints[Random.Range(0, spawnPoints.Length)];
                SpawnScav(randomSpawnPoint);
            }
        }
        
        private void SpawnScav(Transform spawnPoint)
        {
            if (scavPrefab != null)
            {
                GameObject scavObject = Instantiate(scavPrefab, spawnPoint.position, spawnPoint.rotation);
                var scavComponent = scavObject.GetComponent<AIScav>();
                
                if (scavComponent == null)
                {
                    scavComponent = scavObject.AddComponent<AIScav>();
                }
                
                activeScavs.Add(scavComponent);
                Debug.Log($"Spawned scav at {spawnPoint.name}");
            }
        }
    }
}
