using UnityEngine;
using UnityEngine.AI;
using TarkovSystems;

public class TarkovSceneSetup : MonoBehaviour
{
    [Header("Scene Setup")]
    public bool autoSetupScene = true;
    public bool createSampleMap = true;
    
    [Header("Map Generation")]
    public int mapSize = 100;
    public int buildingCount = 5;
    public int lootSpawnCount = 20;
    public int extractionPointCount = 3;
    public int scavSpawnCount = 8;
    
    [Header("Prefabs")]
    public GameObject buildingPrefab;
    public GameObject extractionPrefab;
    public GameObject scavPrefab;
    public GameObject lootSpawnPrefab;
    
    void Start()
    {
        if (autoSetupScene)
        {
            SetupTarkovScene();
        }
    }
    
    [ContextMenu("Setup Tarkov Scene")]
    public void SetupTarkovScene()
    {
        Debug.Log("Setting up Tarkov scene...");
        
        // Create main systems
        CreateGameManager();
        CreateInventorySystem();
        CreateLootSystem();
        CreateExtractionSystem();
        CreateAIScavManager();
        CreateUI();
        
        if (createSampleMap)
        {
            CreateSampleMap();
        }
        
        Debug.Log("Tarkov scene setup complete!");
    }
    
    private void CreateGameManager()
    {
        if (FindFirstObjectByType<TarkovGameManager>() == null)
        {
            GameObject gameManagerObj = new GameObject("TarkovGameManager");
            gameManagerObj.AddComponent<TarkovGameManager>();
            Debug.Log("Created TarkovGameManager");
        }
    }
    
    private void CreateInventorySystem()
    {
        if (FindFirstObjectByType<InventorySystem>() == null)
        {
            GameObject inventoryObj = new GameObject("InventorySystem");
            inventoryObj.AddComponent<InventorySystem>();
            Debug.Log("Created InventorySystem");
        }
    }
    
    private void CreateLootSystem()
    {
        if (FindFirstObjectByType<LootSystem>() == null)
        {
            GameObject lootObj = new GameObject("LootSystem");
            var lootSystem = lootObj.AddComponent<LootSystem>();
            
            // Create some loot spawn points
            for (int i = 0; i < lootSpawnCount; i++)
            {
                Vector3 randomPos = new Vector3(
                    Random.Range(-mapSize/2, mapSize/2),
                    0,
                    Random.Range(-mapSize/2, mapSize/2)
                );
                
                GameObject spawnPoint = new GameObject($"LootSpawn_{i}");
                spawnPoint.transform.position = randomPos;
                spawnPoint.transform.SetParent(lootObj.transform);
                
                // Randomly assign loot tier
                LootTier tier = (LootTier)Random.Range(0, System.Enum.GetValues(typeof(LootTier)).Length);
                lootSystem.AddLootSpawnPoint(spawnPoint.transform, tier);
            }
            
            Debug.Log($"Created LootSystem with {lootSpawnCount} spawn points");
        }
    }
    
    private void CreateExtractionSystem()
    {
        if (FindFirstObjectByType<ExtractionSystem>() == null)
        {
            GameObject extractionObj = new GameObject("ExtractionSystem");
            var extractionSystem = extractionObj.AddComponent<ExtractionSystem>();
            
            // Create extraction points
            for (int i = 0; i < extractionPointCount; i++)
            {
                Vector3 edgePos = GetMapEdgePosition(i);
                
                GameObject extractionPoint = new GameObject($"Extraction_{i}");
                extractionPoint.transform.position = edgePos;
                extractionPoint.transform.SetParent(extractionObj.transform);
                
                // Create extraction zone
                GameObject extractionZone = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
                extractionZone.name = "ExtractionZone";
                extractionZone.transform.SetParent(extractionPoint.transform);
                extractionZone.transform.localPosition = Vector3.zero;
                extractionZone.transform.localScale = new Vector3(6, 0.1f, 6);
                extractionZone.GetComponent<Renderer>().material.color = Color.green;
                
                var collider = extractionZone.GetComponent<Collider>();
                collider.isTrigger = true;
                
                // Add light
                GameObject lightObj = new GameObject("ExtractionLight");
                lightObj.transform.SetParent(extractionPoint.transform);
                lightObj.transform.localPosition = Vector3.up * 5;
                var light = lightObj.AddComponent<Light>();
                light.color = Color.green;
                light.intensity = 2f;
                light.range = 10f;
                
                // Create extraction point data
                var extraction = new ExtractionPoint($"Extraction_{i}", extractionPoint.transform);
                extraction.extractionZone = extractionZone;
                extraction.extractionLight = light;
                extraction.isAlwaysAvailable = i == 0; // First extraction always available
                
                extractionSystem.extractionPoints.Add(extraction);
                
                // Add ExtractionZone component
                var zoneComponent = extractionZone.AddComponent<ExtractionZone>();
                zoneComponent.extractionPoint = extraction;
            }
            
            Debug.Log($"Created ExtractionSystem with {extractionPointCount} extraction points");
        }
    }
    
    private void CreateAIScavManager()
    {
        if (FindFirstObjectByType<AIScavManager>() == null)
        {
            GameObject scavManagerObj = new GameObject("AIScavManager");
            var scavManager = scavManagerObj.AddComponent<AIScavManager>();
            
            // Create scav spawn points
            Transform[] spawnPoints = new Transform[scavSpawnCount];
            for (int i = 0; i < scavSpawnCount; i++)
            {
                Vector3 randomPos = new Vector3(
                    Random.Range(-mapSize/3, mapSize/3),
                    0,
                    Random.Range(-mapSize/3, mapSize/3)
                );
                
                GameObject spawnPoint = new GameObject($"ScavSpawn_{i}");
                spawnPoint.transform.position = randomPos;
                spawnPoint.transform.SetParent(scavManagerObj.transform);
                spawnPoints[i] = spawnPoint.transform;
            }
            
            scavManager.spawnPoints = spawnPoints;
            scavManager.scavPrefab = scavPrefab;
            
            Debug.Log($"Created AIScavManager with {scavSpawnCount} spawn points");
        }
    }
    
    private void CreateUI()
    {
        if (FindFirstObjectByType<TarkovUI>() == null)
        {
            // Create UI Canvas
            GameObject canvasObj = new GameObject("TarkovUI_Canvas");
            var canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
            canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            
            // Add TarkovUI component
            var tarkovUI = canvasObj.AddComponent<TarkovUI>();
            
            // Create basic HUD elements
            CreateHUDElements(canvasObj.transform, tarkovUI);
            
            Debug.Log("Created TarkovUI");
        }
    }
    
    private void CreateHUDElements(Transform canvasTransform, TarkovUI tarkovUI)
    {
        // Create timer text
        GameObject timerObj = new GameObject("RaidTimer");
        timerObj.transform.SetParent(canvasTransform);
        var timerText = timerObj.AddComponent<UnityEngine.UI.Text>();
        timerText.text = "40:00";
        timerText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        timerText.fontSize = 24;
        timerText.color = Color.white;
        
        var timerRect = timerObj.GetComponent<RectTransform>();
        timerRect.anchorMin = new Vector2(0.5f, 1f);
        timerRect.anchorMax = new Vector2(0.5f, 1f);
        timerRect.anchoredPosition = new Vector2(0, -30);
        timerRect.sizeDelta = new Vector2(100, 30);
        
        tarkovUI.raidTimerText = timerText;
        
        // Create health bar
        CreateStatusBar(canvasTransform, "HealthBar", new Vector2(0.1f, 0.1f), Color.red, out var healthBar);
        tarkovUI.healthBar = healthBar;
        
        // Create hydration bar
        CreateStatusBar(canvasTransform, "HydrationBar", new Vector2(0.1f, 0.15f), Color.blue, out var hydrationBar);
        tarkovUI.hydrationBar = hydrationBar;
        
        // Create energy bar
        CreateStatusBar(canvasTransform, "EnergyBar", new Vector2(0.1f, 0.2f), Color.yellow, out var energyBar);
        tarkovUI.energyBar = energyBar;
        
        // Create crosshair
        GameObject crosshairObj = new GameObject("Crosshair");
        crosshairObj.transform.SetParent(canvasTransform);
        var crosshairImage = crosshairObj.AddComponent<UnityEngine.UI.Image>();
        crosshairImage.color = Color.white;
        
        var crosshairRect = crosshairObj.GetComponent<RectTransform>();
        crosshairRect.anchorMin = new Vector2(0.5f, 0.5f);
        crosshairRect.anchorMax = new Vector2(0.5f, 0.5f);
        crosshairRect.anchoredPosition = Vector2.zero;
        crosshairRect.sizeDelta = new Vector2(20, 20);
        
        tarkovUI.crosshair = crosshairObj;
    }
    
    private void CreateStatusBar(Transform parent, string name, Vector2 position, Color color, out UnityEngine.UI.Slider slider)
    {
        GameObject barObj = new GameObject(name);
        barObj.transform.SetParent(parent);
        
        slider = barObj.AddComponent<UnityEngine.UI.Slider>();
        slider.value = 1f;
        
        var rect = barObj.GetComponent<RectTransform>();
        rect.anchorMin = position;
        rect.anchorMax = position;
        rect.anchoredPosition = Vector2.zero;
        rect.sizeDelta = new Vector2(200, 20);
        
        // Create background
        GameObject bg = new GameObject("Background");
        bg.transform.SetParent(barObj.transform);
        var bgImage = bg.AddComponent<UnityEngine.UI.Image>();
        bgImage.color = Color.gray;
        
        // Create fill
        GameObject fill = new GameObject("Fill");
        fill.transform.SetParent(barObj.transform);
        var fillImage = fill.AddComponent<UnityEngine.UI.Image>();
        fillImage.color = color;
        
        slider.targetGraphic = fillImage;
        slider.fillRect = fill.GetComponent<RectTransform>();
    }
    
    private void CreateSampleMap()
    {
        // Create ground plane
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.localScale = new Vector3(mapSize / 10, 1, mapSize / 10);
        ground.GetComponent<Renderer>().material.color = Color.gray;
        
        // Create some buildings
        for (int i = 0; i < buildingCount; i++)
        {
            Vector3 buildingPos = new Vector3(
                Random.Range(-mapSize/3, mapSize/3),
                0,
                Random.Range(-mapSize/3, mapSize/3)
            );
            
            GameObject building = GameObject.CreatePrimitive(PrimitiveType.Cube);
            building.name = $"Building_{i}";
            building.transform.position = buildingPos + Vector3.up * 2.5f;
            building.transform.localScale = new Vector3(
                Random.Range(5, 15),
                Random.Range(3, 8),
                Random.Range(5, 15)
            );
            building.GetComponent<Renderer>().material.color = new Color(0.6f, 0.4f, 0.2f);
        }
        
        // Ensure NavMesh is baked (this would need to be done manually in the editor)
        Debug.Log("Sample map created. Remember to bake NavMesh in Window > AI > Navigation");
    }
    
    private Vector3 GetMapEdgePosition(int index)
    {
        float halfSize = mapSize / 2f;
        
        switch (index % 4)
        {
            case 0: return new Vector3(halfSize - 5, 0, Random.Range(-halfSize/2, halfSize/2));
            case 1: return new Vector3(-halfSize + 5, 0, Random.Range(-halfSize/2, halfSize/2));
            case 2: return new Vector3(Random.Range(-halfSize/2, halfSize/2), 0, halfSize - 5);
            case 3: return new Vector3(Random.Range(-halfSize/2, halfSize/2), 0, -halfSize + 5);
            default: return Vector3.zero;
        }
    }
}
