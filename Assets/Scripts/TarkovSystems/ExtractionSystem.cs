using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TarkovSystems
{
    public enum ExtractionStatus
    {
        Available,
        Unavailable,
        RequiresKey,
        RequiresPayment,
        Conditional
    }
    
    [System.Serializable]
    public class ExtractionPoint
    {
        public string extractionName;
        public Transform extractionTransform;
        public ExtractionStatus status;
        public float extractionTime = 10f;
        public bool isAlwaysAvailable = false;
        public float availabilityChance = 0.7f;
        
        [Header("Requirements")]
        public string requiredKeyId;
        public int paymentAmount;
        public string paymentItemId;
        
        [Header("Visual")]
        public GameObject extractionZone;
        public Light extractionLight;
        public Color availableColor = Color.green;
        public Color unavailableColor = Color.red;
        public Color conditionalColor = Color.yellow;
        
        public bool isCurrentlyAvailable;
        
        public ExtractionPoint(string name, Transform transform)
        {
            extractionName = name;
            extractionTransform = transform;
            status = ExtractionStatus.Available;
        }
    }

    public class ExtractionZone : MonoBehaviour
    {
        [Header("Extraction Settings")]
        public ExtractionPoint extractionPoint;
        public float extractionRadius = 3f;
        
        [Header("UI")]
        public GameObject extractionPrompt;
        public Slider extractionProgressBar;
        public Text extractionText;
        
        private bool playerInZone = false;
        private bool isExtracting = false;
        private float extractionProgress = 0f;
        private GameObject playerObject;
        
        void Start()
        {
            if (extractionPrompt != null)
                extractionPrompt.SetActive(false);
                
            if (extractionProgressBar != null)
                extractionProgressBar.gameObject.SetActive(false);
        }
        
        void Update()
        {
            if (playerInZone && extractionPoint.isCurrentlyAvailable)
            {
                if (Input.GetKeyDown(KeyCode.F) && !isExtracting)
                {
                    StartExtraction();
                }
                
                if (isExtracting)
                {
                    UpdateExtraction();
                }
            }
            
            if (isExtracting && Input.GetKeyDown(KeyCode.Escape))
            {
                CancelExtraction();
            }
        }
        
        private void OnTriggerEnter(Collider other)
        {
            if (other.CompareTag("Player"))
            {
                playerInZone = true;
                playerObject = other.gameObject;
                
                if (extractionPoint.isCurrentlyAvailable && extractionPrompt != null)
                {
                    extractionPrompt.SetActive(true);
                    UpdateExtractionPrompt();
                }
            }
        }
        
        private void OnTriggerExit(Collider other)
        {
            if (other.CompareTag("Player"))
            {
                playerInZone = false;
                playerObject = null;
                
                if (extractionPrompt != null)
                    extractionPrompt.SetActive(false);
                    
                if (isExtracting)
                    CancelExtraction();
            }
        }
        
        private void StartExtraction()
        {
            if (!CanExtract()) return;
            
            isExtracting = true;
            extractionProgress = 0f;
            
            if (extractionProgressBar != null)
            {
                extractionProgressBar.gameObject.SetActive(true);
                extractionProgressBar.value = 0f;
            }
            
            if (extractionPrompt != null)
                extractionPrompt.SetActive(false);
                
            Debug.Log($"Starting extraction at {extractionPoint.extractionName}");
        }
        
        private void UpdateExtraction()
        {
            extractionProgress += Time.deltaTime;
            
            if (extractionProgressBar != null)
            {
                extractionProgressBar.value = extractionProgress / extractionPoint.extractionTime;
            }
            
            if (extractionProgress >= extractionPoint.extractionTime)
            {
                CompleteExtraction();
            }
        }
        
        private void CompleteExtraction()
        {
            isExtracting = false;
            
            if (extractionProgressBar != null)
                extractionProgressBar.gameObject.SetActive(false);
            
            Debug.Log($"Extraction completed at {extractionPoint.extractionName}");
            
            // Notify the game manager
            var gameManager = FindObjectOfType<TarkovGameManager>();
            if (gameManager != null)
            {
                gameManager.PlayerExtracted(extractionPoint);
            }
        }
        
        private void CancelExtraction()
        {
            isExtracting = false;
            extractionProgress = 0f;
            
            if (extractionProgressBar != null)
                extractionProgressBar.gameObject.SetActive(false);
                
            if (extractionPrompt != null && playerInZone && extractionPoint.isCurrentlyAvailable)
                extractionPrompt.SetActive(true);
                
            Debug.Log("Extraction cancelled");
        }
        
        private bool CanExtract()
        {
            var playerInventory = FindObjectOfType<InventorySystem>();
            
            switch (extractionPoint.status)
            {
                case ExtractionStatus.RequiresKey:
                    // Check if player has required key
                    if (playerInventory != null && !string.IsNullOrEmpty(extractionPoint.requiredKeyId))
                    {
                        // TODO: Check if player has the key in inventory
                        return true; // Placeholder
                    }
                    return false;
                    
                case ExtractionStatus.RequiresPayment:
                    // Check if player has required payment
                    if (playerInventory != null && extractionPoint.paymentAmount > 0)
                    {
                        // TODO: Check if player has enough currency/items
                        return true; // Placeholder
                    }
                    return false;
                    
                case ExtractionStatus.Available:
                    return true;
                    
                default:
                    return false;
            }
        }
        
        private void UpdateExtractionPrompt()
        {
            if (extractionText != null)
            {
                string promptText = $"Press F to extract at {extractionPoint.extractionName}";
                
                switch (extractionPoint.status)
                {
                    case ExtractionStatus.RequiresKey:
                        promptText += $" (Requires {extractionPoint.requiredKeyId})";
                        break;
                    case ExtractionStatus.RequiresPayment:
                        promptText += $" (Costs {extractionPoint.paymentAmount})";
                        break;
                }
                
                extractionText.text = promptText;
            }
        }
    }

    public class ExtractionSystem : MonoBehaviour
    {
        [Header("Extraction Points")]
        public List<ExtractionPoint> extractionPoints;
        
        [Header("Settings")]
        public bool randomizeAvailability = true;
        public int minAvailableExtractions = 2;
        
        void Start()
        {
            InitializeExtractionPoints();
            
            if (randomizeAvailability)
            {
                RandomizeExtractionAvailability();
            }
        }
        
        private void InitializeExtractionPoints()
        {
            foreach (var extraction in extractionPoints)
            {
                // Set initial availability
                if (extraction.isAlwaysAvailable)
                {
                    extraction.isCurrentlyAvailable = true;
                }
                else
                {
                    extraction.isCurrentlyAvailable = Random.value <= extraction.availabilityChance;
                }
                
                // Setup visual indicators
                UpdateExtractionVisuals(extraction);
                
                // Add extraction zone component if not present
                if (extraction.extractionZone != null)
                {
                    var zoneComponent = extraction.extractionZone.GetComponent<ExtractionZone>();
                    if (zoneComponent == null)
                    {
                        zoneComponent = extraction.extractionZone.AddComponent<ExtractionZone>();
                    }
                    zoneComponent.extractionPoint = extraction;
                }
            }
        }
        
        private void RandomizeExtractionAvailability()
        {
            // Ensure minimum number of available extractions
            var availableCount = 0;
            foreach (var extraction in extractionPoints)
            {
                if (extraction.isCurrentlyAvailable)
                    availableCount++;
            }
            
            // If we don't have enough available extractions, randomly enable some
            while (availableCount < minAvailableExtractions)
            {
                var randomExtraction = extractionPoints[Random.Range(0, extractionPoints.Count)];
                if (!randomExtraction.isCurrentlyAvailable && !randomExtraction.isAlwaysAvailable)
                {
                    randomExtraction.isCurrentlyAvailable = true;
                    UpdateExtractionVisuals(randomExtraction);
                    availableCount++;
                }
            }
        }
        
        private void UpdateExtractionVisuals(ExtractionPoint extraction)
        {
            if (extraction.extractionLight != null)
            {
                if (extraction.isCurrentlyAvailable)
                {
                    extraction.extractionLight.color = extraction.availableColor;
                    extraction.extractionLight.enabled = true;
                }
                else
                {
                    extraction.extractionLight.color = extraction.unavailableColor;
                    extraction.extractionLight.enabled = false;
                }
            }
        }
        
        public List<ExtractionPoint> GetAvailableExtractions()
        {
            var available = new List<ExtractionPoint>();
            foreach (var extraction in extractionPoints)
            {
                if (extraction.isCurrentlyAvailable)
                    available.Add(extraction);
            }
            return available;
        }
        
        public void SetExtractionAvailability(string extractionName, bool isAvailable)
        {
            var extraction = extractionPoints.Find(e => e.extractionName == extractionName);
            if (extraction != null)
            {
                extraction.isCurrentlyAvailable = isAvailable;
                UpdateExtractionVisuals(extraction);
            }
        }
    }
}
