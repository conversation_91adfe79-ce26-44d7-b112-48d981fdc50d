using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

namespace TarkovSystems
{
    public class TarkovUI : MonoBehaviour
    {
        [Header("HUD Elements")]
        public Text raidTimerText;
        public Slider healthBar;
        public Slider hydrationBar;
        public Slider energyBar;
        public Slider staminaBar;
        public Text weightText;
        
        [Header("Inventory UI")]
        public GameObject inventoryPanel;
        public GridLayoutGroup inventoryGrid;
        public GameObject inventorySlotPrefab;
        public Text inventoryWeightText;
        
        [Header("Extraction UI")]
        public GameObject extractionListPanel;
        public Transform extractionListContent;
        public GameObject extractionEntryPrefab;
        
        [Header("Crosshair")]
        public GameObject crosshair;
        
        private TarkovGameManager gameManager;
        private InventorySystem inventorySystem;
        private ExtractionSystem extractionSystem;
        private List<GameObject> inventorySlots;
        
        void Start()
        {
            // Find systems
            gameManager = FindFirstObjectByType<TarkovGameManager>();
            inventorySystem = FindFirstObjectByType<InventorySystem>();
            extractionSystem = FindFirstObjectByType<ExtractionSystem>();
            
            // Initialize UI
            InitializeInventoryUI();
            InitializeExtractionUI();
            
            // Hide panels initially
            if (inventoryPanel != null)
                inventoryPanel.SetActive(false);
                
            if (extractionListPanel != null)
                extractionListPanel.SetActive(false);
            
            // Subscribe to events
            if (inventorySystem != null)
                inventorySystem.OnInventoryChanged += UpdateInventoryUI;
        }
        
        void Update()
        {
            UpdateHUD();
            HandleInput();
        }
        
        private void UpdateHUD()
        {
            if (gameManager == null) return;
            
            // Update timer
            if (raidTimerText != null)
            {
                raidTimerText.text = gameManager.GetFormattedTime();
                
                // Change color based on remaining time
                float remainingTime = gameManager.GetRemainingTime();
                if (remainingTime < 300f) // Less than 5 minutes
                {
                    raidTimerText.color = Color.red;
                }
                else if (remainingTime < 600f) // Less than 10 minutes
                {
                    raidTimerText.color = Color.yellow;
                }
                else
                {
                    raidTimerText.color = Color.white;
                }
            }
            
            // Update health bars
            if (healthBar != null)
                healthBar.value = gameManager.GetPlayerHealth() / 100f;
                
            if (hydrationBar != null)
                hydrationBar.value = gameManager.GetPlayerHydration() / 100f;
                
            if (energyBar != null)
                energyBar.value = gameManager.GetPlayerEnergy() / 100f;
                
            if (staminaBar != null)
                staminaBar.value = gameManager.GetPlayerStamina() / 100f;
            
            // Update weight
            if (weightText != null && inventorySystem != null)
            {
                float currentWeight = inventorySystem.GetCurrentWeight();
                float weightPercentage = inventorySystem.GetWeightPercentage();
                weightText.text = $"{currentWeight:F1} kg ({weightPercentage:P0})";
                
                // Change color based on weight
                if (weightPercentage > 0.8f)
                    weightText.color = Color.red;
                else if (weightPercentage > 0.6f)
                    weightText.color = Color.yellow;
                else
                    weightText.color = Color.white;
            }
        }
        
        private void HandleInput()
        {
            // Toggle inventory
            if (Input.GetKeyDown(KeyCode.Tab))
            {
                ToggleInventory();
            }
            
            // Toggle extraction list
            if (Input.GetKeyDown(KeyCode.O))
            {
                ToggleExtractionList();
            }
        }
        
        private void ToggleInventory()
        {
            if (inventoryPanel != null)
            {
                bool isActive = inventoryPanel.activeSelf;
                inventoryPanel.SetActive(!isActive);
                
                // Update cursor and time scale
                if (!isActive)
                {
                    Cursor.lockState = CursorLockMode.None;
                    Cursor.visible = true;
                    Time.timeScale = 0f;
                    UpdateInventoryUI();
                }
                else
                {
                    Cursor.lockState = CursorLockMode.Locked;
                    Cursor.visible = false;
                    Time.timeScale = 1f;
                }
            }
        }
        
        private void ToggleExtractionList()
        {
            if (extractionListPanel != null)
            {
                bool isActive = extractionListPanel.activeSelf;
                extractionListPanel.SetActive(!isActive);
                
                if (!isActive)
                {
                    UpdateExtractionList();
                }
            }
        }
        
        private void InitializeInventoryUI()
        {
            if (inventoryGrid == null || inventorySlotPrefab == null || inventorySystem == null)
                return;
            
            inventorySlots = new List<GameObject>();
            
            // Create inventory grid
            int totalSlots = inventorySystem.inventoryWidth * inventorySystem.inventoryHeight;
            inventoryGrid.constraintCount = inventorySystem.inventoryWidth;
            
            for (int i = 0; i < totalSlots; i++)
            {
                GameObject slot = Instantiate(inventorySlotPrefab, inventoryGrid.transform);
                inventorySlots.Add(slot);
                
                // Add click handler
                var button = slot.GetComponent<Button>();
                if (button != null)
                {
                    int slotIndex = i;
                    button.onClick.AddListener(() => OnInventorySlotClicked(slotIndex));
                }
            }
        }
        
        private void UpdateInventoryUI()
        {
            if (inventorySystem == null || inventorySlots == null)
                return;
            
            // Update weight display
            if (inventoryWeightText != null)
            {
                float currentWeight = inventorySystem.GetCurrentWeight();
                float weightPercentage = inventorySystem.GetWeightPercentage();
                inventoryWeightText.text = $"Weight: {currentWeight:F1} kg ({weightPercentage:P0})";
            }
            
            // Update inventory slots
            for (int i = 0; i < inventorySlots.Count; i++)
            {
                int x = i % inventorySystem.inventoryWidth;
                int y = i / inventorySystem.inventoryWidth;
                
                var item = inventorySystem.GetItemAt(x, y);
                var slotImage = inventorySlots[i].GetComponent<Image>();
                var slotText = inventorySlots[i].GetComponentInChildren<Text>();
                
                if (item != null)
                {
                    // Show item
                    if (slotImage != null)
                    {
                        slotImage.color = Color.gray;
                        if (item.icon != null)
                            slotImage.sprite = item.icon;
                    }
                    
                    if (slotText != null)
                        slotText.text = item.itemName;
                }
                else
                {
                    // Empty slot
                    if (slotImage != null)
                    {
                        slotImage.color = Color.white;
                        slotImage.sprite = null;
                    }
                    
                    if (slotText != null)
                        slotText.text = "";
                }
            }
        }
        
        private void OnInventorySlotClicked(int slotIndex)
        {
            int x = slotIndex % inventorySystem.inventoryWidth;
            int y = slotIndex / inventorySystem.inventoryWidth;
            
            var item = inventorySystem.GetItemAt(x, y);
            if (item != null)
            {
                Debug.Log($"Clicked on {item.itemName}");
                // TODO: Implement item interaction (use, move, etc.)
            }
        }
        
        private void InitializeExtractionUI()
        {
            // Will be populated when extraction list is opened
        }
        
        private void UpdateExtractionList()
        {
            if (extractionSystem == null || extractionListContent == null || extractionEntryPrefab == null)
                return;
            
            // Clear existing entries
            foreach (Transform child in extractionListContent)
            {
                Destroy(child.gameObject);
            }
            
            // Add available extractions
            var availableExtractions = extractionSystem.GetAvailableExtractions();
            
            foreach (var extraction in availableExtractions)
            {
                GameObject entry = Instantiate(extractionEntryPrefab, extractionListContent);
                var entryText = entry.GetComponentInChildren<Text>();
                
                if (entryText != null)
                {
                    string statusText = extraction.isCurrentlyAvailable ? "[OPEN]" : "[CLOSED]";
                    entryText.text = $"{extraction.extractionName} {statusText}";
                    entryText.color = extraction.isCurrentlyAvailable ? Color.green : Color.red;
                }
            }
        }
        
        public void ShowDamageIndicator(Vector3 damageDirection)
        {
            // TODO: Implement damage direction indicator
            Debug.Log("Damage taken from direction: " + damageDirection);
        }
        
        public void ShowLootPrompt(string itemName)
        {
            // TODO: Implement loot prompt UI
            Debug.Log("Loot available: " + itemName);
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (inventorySystem != null)
                inventorySystem.OnInventoryChanged -= UpdateInventoryUI;
        }
    }
}
