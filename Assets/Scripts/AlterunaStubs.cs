// Stub classes to replace missing Alteruna networking dependencies
// This allows the project to compile without the full Alteruna package

using UnityEngine;
using System;
using System.Collections.Generic;

namespace Alteruna
{
    // Base networking classes
    public abstract class Synchronizable : MonoBehaviour
    {
        public virtual void Awake() { }
        public virtual void Start() { }
        public virtual void Update() { }
    }
    
    public abstract class InputSynchronizable : Synchronizable
    {
        public virtual void FixedUpdate() { }
    }
    
    // Input synchronization classes
    [System.Serializable]
    public class SyncedAxis
    {
        public string axisName;
        public float value;
        
        public SyncedAxis(string name)
        {
            axisName = name;
            value = 0f;
        }
        
        public float GetValue()
        {
            return Input.GetAxis(axisName);
        }
    }
    
    [System.Serializable]
    public class SyncedKey
    {
        public KeyCode keyCode;
        public bool isPressed;
        
        public SyncedKey(KeyCode key)
        {
            keyCode = key;
            isPressed = false;
        }
        
        public bool GetKey()
        {
            return Input.GetKey(keyCode);
        }
        
        public bool GetKeyDown()
        {
            return Input.GetKeyDown(keyCode);
        }
        
        public bool GetKeyUp()
        {
            return Input.GetKeyUp(keyCode);
        }
    }
    
    // Networking transport classes
    public interface ITransportStreamWriter
    {
        void WriteFloat(float value);
        void WriteInt(int value);
        void WriteBool(bool value);
        void WriteVector3(Vector3 value);
        void WriteQuaternion(Quaternion value);
    }
    
    public class Writer : ITransportStreamWriter
    {
        public void WriteFloat(float value) { }
        public void WriteInt(int value) { }
        public void WriteBool(bool value) { }
        public void WriteVector3(Vector3 value) { }
        public void WriteQuaternion(Quaternion value) { }
    }
    
    public class Reader
    {
        public float ReadFloat() { return 0f; }
        public int ReadInt() { return 0; }
        public bool ReadBool() { return false; }
        public Vector3 ReadVector3() { return Vector3.zero; }
        public Quaternion ReadQuaternion() { return Quaternion.identity; }
    }
    
    // Multiplayer classes
    public class Multiplayer : MonoBehaviour
    {
        public static Multiplayer Instance { get; private set; }
        
        void Awake()
        {
            if (Instance == null)
                Instance = this;
        }
        
        public Room GetRoom() { return new Room(); }
        public User GetUser() { return new User(); }
        public List<User> GetUsers() { return new List<User>(); }
    }
    
    public class Room
    {
        public string Name { get; set; } = "Local Room";
        public List<User> Users { get; set; } = new List<User>();
    }
    
    public class User
    {
        public string Name { get; set; } = "Local Player";
        public int Index { get; set; } = 0;
        public bool IsMe { get; set; } = true;
    }
    
    public class Endpoint
    {
        public string Address { get; set; } = "localhost";
        public int Port { get; set; } = 7777;
    }
    
    // Communication bridge
    public class CommunicationBridge : MonoBehaviour
    {
        public static CommunicationBridge Instance { get; private set; }
        
        void Awake()
        {
            if (Instance == null)
                Instance = this;
        }
        
        public void InvokeRemoteMethod(string methodName, params object[] parameters) { }
    }
    
    // Score system interfaces
    public interface IScoreObject
    {
        string GetName();
        int GetScore();
        void SetScore(int score);
    }
    
    public enum ScoreType
    {
        Kills,
        Deaths,
        Score
    }
    
    // Attributes
    public class SynchronizableMethodAttribute : Attribute
    {
        public SynchronizableMethodAttribute() { }
    }
    
    public class SynchronizableMethod : SynchronizableMethodAttribute
    {
        public SynchronizableMethod() { }
    }
}

// Additional stub for Easy Weapons integration
public class Weapon : MonoBehaviour
{
    [Header("Weapon Stats")]
    public float damage = 50f;
    public float fireRate = 0.1f;
    public float range = 100f;
    public float accuracy = 1f;
    public int ammoCapacity = 30;
    public int currentAmmo = 30;
    
    [Header("Audio")]
    public AudioClip fireSound;
    public AudioClip reloadSound;
    
    [Header("Effects")]
    public GameObject muzzleFlash;
    public GameObject bulletImpact;
    
    private AudioSource audioSource;
    private float lastFireTime;
    
    void Start()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();
    }
    
    public virtual void Fire()
    {
        if (Time.time - lastFireTime < fireRate) return;
        if (currentAmmo <= 0) return;
        
        lastFireTime = Time.time;
        currentAmmo--;
        
        // Play sound
        if (fireSound != null && audioSource != null)
            audioSource.PlayOneShot(fireSound);
        
        // Show muzzle flash
        if (muzzleFlash != null)
        {
            muzzleFlash.SetActive(true);
            Invoke(nameof(HideMuzzleFlash), 0.1f);
        }
        
        // Raycast for hit detection
        RaycastHit hit;
        Vector3 fireDirection = transform.forward;
        
        if (Physics.Raycast(transform.position, fireDirection, out hit, range))
        {
            // Handle hit
            var health = hit.collider.GetComponent<Health>();
            if (health != null)
            {
                health.TakeDamage(damage);
            }
            
            // Spawn impact effect
            if (bulletImpact != null)
            {
                Instantiate(bulletImpact, hit.point, Quaternion.LookRotation(hit.normal));
            }
        }
    }
    
    public virtual void Reload()
    {
        if (reloadSound != null && audioSource != null)
            audioSource.PlayOneShot(reloadSound);
        
        currentAmmo = ammoCapacity;
    }
    
    private void HideMuzzleFlash()
    {
        if (muzzleFlash != null)
            muzzleFlash.SetActive(false);
    }
}

// Health component for compatibility
public class Health : MonoBehaviour
{
    [Header("Health Settings")]
    public float maxHealth = 100f;
    public float currentHealth = 100f;
    
    public delegate void HealthChangedHandler(float newHealth, float maxHealth);
    public event HealthChangedHandler OnHealthChanged;
    
    public delegate void DeathHandler();
    public event DeathHandler OnDeath;
    
    void Start()
    {
        currentHealth = maxHealth;
    }
    
    public void TakeDamage(float damage)
    {
        currentHealth -= damage;
        currentHealth = Mathf.Clamp(currentHealth, 0f, maxHealth);
        
        OnHealthChanged?.Invoke(currentHealth, maxHealth);
        
        if (currentHealth <= 0f)
        {
            Die();
        }
    }
    
    public void Heal(float healAmount)
    {
        currentHealth += healAmount;
        currentHealth = Mathf.Clamp(currentHealth, 0f, maxHealth);
        
        OnHealthChanged?.Invoke(currentHealth, maxHealth);
    }
    
    public void SetHealth(float health)
    {
        currentHealth = Mathf.Clamp(health, 0f, maxHealth);
        OnHealthChanged?.Invoke(currentHealth, maxHealth);
        
        if (currentHealth <= 0f)
        {
            Die();
        }
    }
    
    public float GetHealthPercentage()
    {
        return currentHealth / maxHealth;
    }
    
    public bool IsAlive()
    {
        return currentHealth > 0f;
    }
    
    private void Die()
    {
        OnDeath?.Invoke();
        Debug.Log($"{gameObject.name} died!");
    }
}
