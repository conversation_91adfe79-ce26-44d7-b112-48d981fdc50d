using UnityEngine;
using TarkovSystems;

public class TarkovTestSetup : MonoBehaviour
{
    [Header("Test Setup")]
    public bool runTestOnStart = true;
    public bool createTestEnvironment = true;
    
    void Start()
    {
        if (runTestOnStart)
        {
            SetupTestEnvironment();
        }
    }
    
    [ContextMenu("Setup Test Environment")]
    public void SetupTestEnvironment()
    {
        Debug.Log("Setting up Tarkov test environment...");
        
        // Ensure we have a TarkovGameManager
        var gameManager = FindFirstObjectByType<TarkovGameManager>();
        if (gameManager == null)
        {
            GameObject gmObj = new GameObject("TarkovGameManager");
            gameManager = gmObj.AddComponent<TarkovGameManager>();
            Debug.Log("Created TarkovGameManager");
        }
        
        // Create basic systems
        CreateInventorySystem();
        CreateLootSystem();
        CreateExtractionSystem();
        
        if (createTestEnvironment)
        {
            CreateTestMap();
        }
        
        Debug.Log("Test environment setup complete!");
        Debug.Log("Controls:");
        Debug.Log("- Tab: Toggle Inventory");
        Debug.Log("- H: Quick Heal");
        Debug.Log("- F: Interact with loot/extraction");
        Debug.Log("- O: Toggle extraction list");
    }
    
    private void CreateInventorySystem()
    {
        var existing = FindFirstObjectByType<InventorySystem>();
        if (existing == null)
        {
            GameObject invObj = new GameObject("InventorySystem");
            var invSystem = invObj.AddComponent<InventorySystem>();
            
            // Add some test items
            var testItem1 = new InventoryItem("test_medkit", "Medkit", ItemType.Medical, 1, 1);
            testItem1.weight = 0.5f;
            
            var testItem2 = new InventoryItem("test_ammo", "5.56 Ammo", ItemType.Ammo, 1, 1);
            testItem2.weight = 0.1f;
            
            invSystem.TryAddItem(testItem1);
            invSystem.TryAddItem(testItem2);
            
            Debug.Log("Created InventorySystem with test items");
        }
    }
    
    private void CreateLootSystem()
    {
        var existing = FindFirstObjectByType<LootSystem>();
        if (existing == null)
        {
            GameObject lootObj = new GameObject("LootSystem");
            var lootSystem = lootObj.AddComponent<LootSystem>();
            
            // Create a few test loot spawns
            for (int i = 0; i < 3; i++)
            {
                Vector3 pos = new Vector3(i * 5f, 0, 0);
                GameObject spawnPoint = new GameObject($"TestLootSpawn_{i}");
                spawnPoint.transform.position = pos;
                spawnPoint.transform.SetParent(lootObj.transform);
                
                // Add visual indicator
                var cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
                cube.transform.SetParent(spawnPoint.transform);
                cube.transform.localPosition = Vector3.up;
                cube.transform.localScale = Vector3.one * 0.5f;
                cube.GetComponent<Renderer>().material.color = Color.yellow;
                
                lootSystem.AddLootSpawnPoint(spawnPoint.transform, LootTier.Common);
            }
            
            Debug.Log("Created LootSystem with test spawn points");
        }
    }
    
    private void CreateExtractionSystem()
    {
        var existing = FindFirstObjectByType<ExtractionSystem>();
        if (existing == null)
        {
            GameObject extObj = new GameObject("ExtractionSystem");
            var extSystem = extObj.AddComponent<ExtractionSystem>();
            
            // Create test extraction point
            Vector3 extPos = new Vector3(10f, 0, 10f);
            GameObject extPoint = new GameObject("TestExtraction");
            extPoint.transform.position = extPos;
            extPoint.transform.SetParent(extObj.transform);
            
            // Create extraction zone
            GameObject extZone = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            extZone.name = "ExtractionZone";
            extZone.transform.SetParent(extPoint.transform);
            extZone.transform.localPosition = Vector3.zero;
            extZone.transform.localScale = new Vector3(4, 0.1f, 4);
            extZone.GetComponent<Renderer>().material.color = Color.green;
            extZone.GetComponent<Collider>().isTrigger = true;
            
            // Add light
            GameObject lightObj = new GameObject("ExtractionLight");
            lightObj.transform.SetParent(extPoint.transform);
            lightObj.transform.localPosition = Vector3.up * 3;
            var light = lightObj.AddComponent<Light>();
            light.color = Color.green;
            light.intensity = 2f;
            light.range = 8f;
            
            // Create extraction point data
            var extraction = new ExtractionPoint("Test Extraction", extPoint.transform);
            extraction.extractionZone = extZone;
            extraction.extractionLight = light;
            extraction.isAlwaysAvailable = true;
            
            extSystem.extractionPoints.Add(extraction);
            
            // Add ExtractionZone component
            var zoneComponent = extZone.AddComponent<ExtractionZone>();
            zoneComponent.extractionPoint = extraction;
            
            Debug.Log("Created ExtractionSystem with test extraction point");
        }
    }
    
    private void CreateTestMap()
    {
        // Create ground
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "TestGround";
        ground.transform.localScale = new Vector3(5, 1, 5);
        ground.GetComponent<Renderer>().material.color = Color.gray;
        
        // Create some walls for cover
        for (int i = 0; i < 3; i++)
        {
            GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            wall.name = $"TestWall_{i}";
            wall.transform.position = new Vector3(i * 3f - 3f, 1f, 5f);
            wall.transform.localScale = new Vector3(2, 2, 0.5f);
            wall.GetComponent<Renderer>().material.color = Color.brown;
        }
        
        // Create player spawn point
        GameObject playerSpawn = new GameObject("PlayerSpawn");
        playerSpawn.transform.position = new Vector3(0, 1, -5);
        
        // Add visual indicator
        var sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        sphere.transform.SetParent(playerSpawn.transform);
        sphere.transform.localPosition = Vector3.zero;
        sphere.transform.localScale = Vector3.one * 0.5f;
        sphere.GetComponent<Renderer>().material.color = Color.blue;
        
        Debug.Log("Created test map environment");
    }
    
    [ContextMenu("Test Inventory")]
    public void TestInventory()
    {
        var inventory = FindFirstObjectByType<InventorySystem>();
        if (inventory != null)
        {
            Debug.Log($"Inventory Weight: {inventory.GetCurrentWeight()}/{inventory.maxWeight}");
            Debug.Log($"Inventory Slots Used: {inventory.GetUsedSlots()}/{inventory.inventoryWidth * inventory.inventoryHeight}");
        }
        else
        {
            Debug.LogWarning("No InventorySystem found!");
        }
    }
    
    [ContextMenu("Test Loot Spawn")]
    public void TestLootSpawn()
    {
        var lootSystem = FindFirstObjectByType<LootSystem>();
        if (lootSystem != null)
        {
            lootSystem.SpawnLootAtAllPoints();
            Debug.Log("Spawned loot at all spawn points");
        }
        else
        {
            Debug.LogWarning("No LootSystem found!");
        }
    }
    
    [ContextMenu("Test Extraction")]
    public void TestExtraction()
    {
        var extractionSystem = FindFirstObjectByType<ExtractionSystem>();
        if (extractionSystem != null)
        {
            var availableExtractions = extractionSystem.GetAvailableExtractions();
            Debug.Log($"Available extractions: {availableExtractions.Count}");
            
            foreach (var extraction in availableExtractions)
            {
                Debug.Log($"- {extraction.extractionName}: {(extraction.isCurrentlyAvailable ? "OPEN" : "CLOSED")}");
            }
        }
        else
        {
            Debug.LogWarning("No ExtractionSystem found!");
        }
    }
    
    void Update()
    {
        // Quick test controls
        if (Input.GetKeyDown(KeyCode.F1))
        {
            TestInventory();
        }
        
        if (Input.GetKeyDown(KeyCode.F2))
        {
            TestLootSpawn();
        }
        
        if (Input.GetKeyDown(KeyCode.F3))
        {
            TestExtraction();
        }
        
        if (Input.GetKeyDown(KeyCode.F4))
        {
            SetupTestEnvironment();
        }
    }
}
