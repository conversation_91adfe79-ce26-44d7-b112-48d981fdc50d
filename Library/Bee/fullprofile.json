{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 30682, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 30682, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 30682, "tid": 26, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 30682, "tid": 26, "ts": 1751677892579074, "dur": 9, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 30682, "tid": 26, "ts": 1751677892579112, "dur": 8, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 30682, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 30682, "tid": 1, "ts": 1751677892239769, "dur": 2682, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30682, "tid": 1, "ts": 1751677892242455, "dur": 52646, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 30682, "tid": 1, "ts": 1751677892295102, "dur": 29509, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 30682, "tid": 26, "ts": 1751677892579126, "dur": 22, "ph": "X", "name": "", "args": {}}, {"pid": 30682, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892239678, "dur": 12488, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892252168, "dur": 326190, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892252592, "dur": 531, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892253250, "dur": 23, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892253279, "dur": 1420, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892254709, "dur": 4, "ph": "X", "name": "ProcessMessages 6971", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892254714, "dur": 327, "ph": "X", "name": "ReadAsync 6971", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892255300, "dur": 3, "ph": "X", "name": "ProcessMessages 6774", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892255304, "dur": 993, "ph": "X", "name": "ReadAsync 6774", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892256581, "dur": 4, "ph": "X", "name": "ProcessMessages 7558", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892256586, "dur": 55, "ph": "X", "name": "ReadAsync 7558", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892256648, "dur": 3, "ph": "X", "name": "ProcessMessages 8133", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892256651, "dur": 54, "ph": "X", "name": "ReadAsync 8133", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257053, "dur": 1, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257057, "dur": 89, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257153, "dur": 4, "ph": "X", "name": "ProcessMessages 8126", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257158, "dur": 72, "ph": "X", "name": "ReadAsync 8126", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257232, "dur": 1, "ph": "X", "name": "ProcessMessages 1237", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257248, "dur": 245, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257507, "dur": 2, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257511, "dur": 88, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257604, "dur": 3, "ph": "X", "name": "ProcessMessages 3840", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257639, "dur": 91, "ph": "X", "name": "ReadAsync 3840", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257732, "dur": 2, "ph": "X", "name": "ProcessMessages 2397", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257735, "dur": 42, "ph": "X", "name": "ReadAsync 2397", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257779, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892257781, "dur": 283, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258080, "dur": 25, "ph": "X", "name": "ProcessMessages 4142", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258107, "dur": 59, "ph": "X", "name": "ReadAsync 4142", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258170, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258172, "dur": 46, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258220, "dur": 1, "ph": "X", "name": "ProcessMessages 1248", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258221, "dur": 40, "ph": "X", "name": "ReadAsync 1248", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258265, "dur": 212, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258481, "dur": 4, "ph": "X", "name": "ProcessMessages 4491", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258489, "dur": 49, "ph": "X", "name": "ReadAsync 4491", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258542, "dur": 5, "ph": "X", "name": "ProcessMessages 1455", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258549, "dur": 73, "ph": "X", "name": "ReadAsync 1455", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258624, "dur": 1, "ph": "X", "name": "ProcessMessages 1621", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258626, "dur": 47, "ph": "X", "name": "ReadAsync 1621", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258676, "dur": 64, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258742, "dur": 1, "ph": "X", "name": "ProcessMessages 1205", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258744, "dur": 63, "ph": "X", "name": "ReadAsync 1205", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258813, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258815, "dur": 72, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258888, "dur": 1, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892258890, "dur": 39, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259110, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259112, "dur": 29, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259143, "dur": 3, "ph": "X", "name": "ProcessMessages 4013", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259147, "dur": 44, "ph": "X", "name": "ReadAsync 4013", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259193, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259194, "dur": 38, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259234, "dur": 1, "ph": "X", "name": "ProcessMessages 1395", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259236, "dur": 33, "ph": "X", "name": "ReadAsync 1395", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259270, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259272, "dur": 33, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259326, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259327, "dur": 62, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259391, "dur": 1, "ph": "X", "name": "ProcessMessages 1915", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259393, "dur": 36, "ph": "X", "name": "ReadAsync 1915", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259431, "dur": 4, "ph": "X", "name": "ProcessMessages 1091", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259437, "dur": 40, "ph": "X", "name": "ReadAsync 1091", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259479, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259480, "dur": 41, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259533, "dur": 9, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259545, "dur": 56, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259603, "dur": 1, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259616, "dur": 38, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259661, "dur": 43, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259705, "dur": 1, "ph": "X", "name": "ProcessMessages 1118", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259707, "dur": 58, "ph": "X", "name": "ReadAsync 1118", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259767, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259769, "dur": 123, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259894, "dur": 1, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892259895, "dur": 310, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260210, "dur": 19, "ph": "X", "name": "ProcessMessages 2569", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260231, "dur": 41, "ph": "X", "name": "ReadAsync 2569", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260278, "dur": 3, "ph": "X", "name": "ProcessMessages 5214", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260327, "dur": 68, "ph": "X", "name": "ReadAsync 5214", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260399, "dur": 47, "ph": "X", "name": "ProcessMessages 2493", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260448, "dur": 79, "ph": "X", "name": "ReadAsync 2493", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260539, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260546, "dur": 172, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260720, "dur": 1, "ph": "X", "name": "ProcessMessages 1449", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260722, "dur": 117, "ph": "X", "name": "ReadAsync 1449", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260841, "dur": 2, "ph": "X", "name": "ProcessMessages 2154", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260844, "dur": 46, "ph": "X", "name": "ReadAsync 2154", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260892, "dur": 71, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260965, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892260967, "dur": 49, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261019, "dur": 50, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261071, "dur": 1, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261072, "dur": 25, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261100, "dur": 162, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261264, "dur": 2, "ph": "X", "name": "ProcessMessages 1359", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261267, "dur": 46, "ph": "X", "name": "ReadAsync 1359", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261696, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261698, "dur": 92, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261806, "dur": 4, "ph": "X", "name": "ProcessMessages 8190", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261812, "dur": 37, "ph": "X", "name": "ReadAsync 8190", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261852, "dur": 44, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261898, "dur": 1, "ph": "X", "name": "ProcessMessages 1238", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261899, "dur": 41, "ph": "X", "name": "ReadAsync 1238", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892261948, "dur": 94, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262044, "dur": 1, "ph": "X", "name": "ProcessMessages 1765", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262046, "dur": 43, "ph": "X", "name": "ReadAsync 1765", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262090, "dur": 1, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262092, "dur": 34, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262128, "dur": 57, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262187, "dur": 1, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262188, "dur": 51, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262240, "dur": 2, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262244, "dur": 48, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262293, "dur": 1, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262298, "dur": 55, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262356, "dur": 1, "ph": "X", "name": "ProcessMessages 1495", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262358, "dur": 32, "ph": "X", "name": "ReadAsync 1495", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262393, "dur": 49, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262443, "dur": 1, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262445, "dur": 48, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262495, "dur": 1, "ph": "X", "name": "ProcessMessages 1522", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262497, "dur": 36, "ph": "X", "name": "ReadAsync 1522", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262535, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262536, "dur": 42, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262579, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262581, "dur": 58, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262640, "dur": 1, "ph": "X", "name": "ProcessMessages 1368", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262642, "dur": 84, "ph": "X", "name": "ReadAsync 1368", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262743, "dur": 1, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262745, "dur": 40, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262787, "dur": 1, "ph": "X", "name": "ProcessMessages 1638", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262789, "dur": 93, "ph": "X", "name": "ReadAsync 1638", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262899, "dur": 47, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262947, "dur": 1, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262949, "dur": 37, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262988, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892262990, "dur": 45, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263036, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263058, "dur": 86, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263145, "dur": 1, "ph": "X", "name": "ProcessMessages 1703", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263146, "dur": 147, "ph": "X", "name": "ReadAsync 1703", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263294, "dur": 1, "ph": "X", "name": "ProcessMessages 1874", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263296, "dur": 93, "ph": "X", "name": "ReadAsync 1874", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263396, "dur": 1, "ph": "X", "name": "ProcessMessages 2380", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263398, "dur": 173, "ph": "X", "name": "ReadAsync 2380", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263573, "dur": 1, "ph": "X", "name": "ProcessMessages 2253", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263575, "dur": 41, "ph": "X", "name": "ReadAsync 2253", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263618, "dur": 1, "ph": "X", "name": "ProcessMessages 1311", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263655, "dur": 111, "ph": "X", "name": "ReadAsync 1311", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263778, "dur": 2, "ph": "X", "name": "ProcessMessages 1335", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263782, "dur": 53, "ph": "X", "name": "ReadAsync 1335", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263837, "dur": 15, "ph": "X", "name": "ProcessMessages 1776", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263855, "dur": 141, "ph": "X", "name": "ReadAsync 1776", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892263998, "dur": 2, "ph": "X", "name": "ProcessMessages 2788", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264001, "dur": 51, "ph": "X", "name": "ReadAsync 2788", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264054, "dur": 1, "ph": "X", "name": "ProcessMessages 2094", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264056, "dur": 55, "ph": "X", "name": "ReadAsync 2094", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264113, "dur": 1, "ph": "X", "name": "ProcessMessages 1046", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264120, "dur": 83, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264206, "dur": 2, "ph": "X", "name": "ProcessMessages 2110", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264231, "dur": 97, "ph": "X", "name": "ReadAsync 2110", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264330, "dur": 1, "ph": "X", "name": "ProcessMessages 1447", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264332, "dur": 89, "ph": "X", "name": "ReadAsync 1447", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264423, "dur": 2, "ph": "X", "name": "ProcessMessages 1920", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264426, "dur": 82, "ph": "X", "name": "ReadAsync 1920", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264510, "dur": 2, "ph": "X", "name": "ProcessMessages 2717", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264514, "dur": 178, "ph": "X", "name": "ReadAsync 2717", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264693, "dur": 1, "ph": "X", "name": "ProcessMessages 1383", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264695, "dur": 43, "ph": "X", "name": "ReadAsync 1383", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264745, "dur": 48, "ph": "X", "name": "ReadAsync 1265", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264795, "dur": 12, "ph": "X", "name": "ProcessMessages 1241", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264825, "dur": 61, "ph": "X", "name": "ReadAsync 1241", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264890, "dur": 1, "ph": "X", "name": "ProcessMessages 1504", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264893, "dur": 101, "ph": "X", "name": "ReadAsync 1504", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892264997, "dur": 65, "ph": "X", "name": "ReadAsync 1199", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265064, "dur": 2, "ph": "X", "name": "ProcessMessages 1885", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265067, "dur": 51, "ph": "X", "name": "ReadAsync 1885", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265123, "dur": 1, "ph": "X", "name": "ProcessMessages 1603", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265125, "dur": 57, "ph": "X", "name": "ReadAsync 1603", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265205, "dur": 1, "ph": "X", "name": "ProcessMessages 960", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265207, "dur": 82, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265290, "dur": 1, "ph": "X", "name": "ProcessMessages 1887", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265293, "dur": 47, "ph": "X", "name": "ReadAsync 1887", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265343, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265347, "dur": 139, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265487, "dur": 1, "ph": "X", "name": "ProcessMessages 1530", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265491, "dur": 120, "ph": "X", "name": "ReadAsync 1530", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265612, "dur": 1, "ph": "X", "name": "ProcessMessages 1977", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265624, "dur": 63, "ph": "X", "name": "ReadAsync 1977", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265690, "dur": 52, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892265745, "dur": 603, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892266351, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892266471, "dur": 58, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892266532, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892266567, "dur": 170, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892266738, "dur": 1, "ph": "X", "name": "ProcessMessages 1777", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892266741, "dur": 52, "ph": "X", "name": "ReadAsync 1777", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892266797, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892266800, "dur": 263, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267066, "dur": 2, "ph": "X", "name": "ProcessMessages 1321", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267087, "dur": 75, "ph": "X", "name": "ReadAsync 1321", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267164, "dur": 4, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267170, "dur": 40, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267212, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267213, "dur": 46, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267265, "dur": 104, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267370, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267380, "dur": 38, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267420, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267423, "dur": 125, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267549, "dur": 1, "ph": "X", "name": "ProcessMessages 1289", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267551, "dur": 38, "ph": "X", "name": "ReadAsync 1289", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267591, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267593, "dur": 56, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267652, "dur": 9, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267662, "dur": 62, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267733, "dur": 1, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267736, "dur": 62, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267815, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267817, "dur": 70, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267889, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892267891, "dur": 125, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892268017, "dur": 10, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892268029, "dur": 102, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892268139, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892268143, "dur": 473, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892268618, "dur": 24, "ph": "X", "name": "ProcessMessages 4846", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892268674, "dur": 88, "ph": "X", "name": "ReadAsync 4846", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892268766, "dur": 147, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892268920, "dur": 33, "ph": "X", "name": "ProcessMessages 1584", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892268955, "dur": 59, "ph": "X", "name": "ReadAsync 1584", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892269018, "dur": 2, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892269022, "dur": 167, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892269191, "dur": 40, "ph": "X", "name": "ProcessMessages 1707", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892269233, "dur": 86, "ph": "X", "name": "ReadAsync 1707", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892269325, "dur": 50, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892269378, "dur": 445, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892269842, "dur": 2, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892269846, "dur": 87, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892269936, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892269940, "dur": 140, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270082, "dur": 1, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270085, "dur": 44, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270131, "dur": 1, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270134, "dur": 49, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270185, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270195, "dur": 118, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270319, "dur": 2, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270323, "dur": 50, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270375, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270376, "dur": 56, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270434, "dur": 1, "ph": "X", "name": "ProcessMessages 1241", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270436, "dur": 62, "ph": "X", "name": "ReadAsync 1241", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270508, "dur": 76, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270586, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270588, "dur": 32, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270655, "dur": 2, "ph": "X", "name": "ProcessMessages 1134", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270660, "dur": 38, "ph": "X", "name": "ReadAsync 1134", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270700, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270702, "dur": 104, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270809, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270811, "dur": 88, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270905, "dur": 4, "ph": "X", "name": "ProcessMessages 1282", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270914, "dur": 60, "ph": "X", "name": "ReadAsync 1282", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270977, "dur": 1, "ph": "X", "name": "ProcessMessages 1453", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892270980, "dur": 71, "ph": "X", "name": "ReadAsync 1453", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271052, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271054, "dur": 58, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271114, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271117, "dur": 106, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271244, "dur": 1, "ph": "X", "name": "ProcessMessages 1209", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271247, "dur": 97, "ph": "X", "name": "ReadAsync 1209", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271345, "dur": 11, "ph": "X", "name": "ProcessMessages 2352", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271357, "dur": 269, "ph": "X", "name": "ReadAsync 2352", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271638, "dur": 1, "ph": "X", "name": "ProcessMessages 1134", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271639, "dur": 44, "ph": "X", "name": "ReadAsync 1134", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271685, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271687, "dur": 63, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271752, "dur": 6, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271759, "dur": 104, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271873, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271879, "dur": 67, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271948, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892271951, "dur": 113, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272073, "dur": 1, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272076, "dur": 73, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272202, "dur": 59, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272263, "dur": 61, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272326, "dur": 1, "ph": "X", "name": "ProcessMessages 2074", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272328, "dur": 71, "ph": "X", "name": "ReadAsync 2074", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272400, "dur": 2, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272404, "dur": 92, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272498, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272502, "dur": 187, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272690, "dur": 1, "ph": "X", "name": "ProcessMessages 1725", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892272692, "dur": 377, "ph": "X", "name": "ReadAsync 1725", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273072, "dur": 53, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273130, "dur": 3, "ph": "X", "name": "ProcessMessages 3267", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273134, "dur": 162, "ph": "X", "name": "ReadAsync 3267", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273299, "dur": 2, "ph": "X", "name": "ProcessMessages 1776", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273303, "dur": 101, "ph": "X", "name": "ReadAsync 1776", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273406, "dur": 15, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273424, "dur": 67, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273493, "dur": 3, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273503, "dur": 36, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273548, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273552, "dur": 58, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273619, "dur": 2, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273643, "dur": 44, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273690, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273710, "dur": 57, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273769, "dur": 14, "ph": "X", "name": "ProcessMessages 921", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273784, "dur": 27, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273817, "dur": 33, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273852, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273856, "dur": 38, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273895, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892273897, "dur": 82, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274000, "dur": 4, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274006, "dur": 53, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274061, "dur": 2, "ph": "X", "name": "ProcessMessages 2165", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274064, "dur": 59, "ph": "X", "name": "ReadAsync 2165", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274125, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274128, "dur": 194, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274324, "dur": 1, "ph": "X", "name": "ProcessMessages 1341", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274326, "dur": 109, "ph": "X", "name": "ReadAsync 1341", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274466, "dur": 2, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274471, "dur": 142, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274615, "dur": 3, "ph": "X", "name": "ProcessMessages 2395", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274621, "dur": 49, "ph": "X", "name": "ReadAsync 2395", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274673, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274675, "dur": 166, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892274845, "dur": 323, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275204, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275210, "dur": 73, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275322, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275325, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275399, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275402, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275483, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275486, "dur": 115, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275607, "dur": 12, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275622, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275778, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275856, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275858, "dur": 88, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275961, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892275963, "dur": 202, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892276226, "dur": 7, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892276233, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892276318, "dur": 11, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892276331, "dur": 195, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892276530, "dur": 74, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892276615, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892276735, "dur": 220, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892276958, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892276961, "dur": 154, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277136, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277139, "dur": 234, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277374, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277376, "dur": 63, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277442, "dur": 203, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277649, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277652, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277700, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277704, "dur": 82, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277797, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892277840, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278042, "dur": 64, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278108, "dur": 13, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278126, "dur": 98, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278230, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278231, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278279, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278281, "dur": 171, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278467, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278469, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278527, "dur": 114, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278644, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278646, "dur": 231, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278878, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278880, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278949, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892278951, "dur": 231, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892279212, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892279226, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892279295, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892279301, "dur": 209, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892279512, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892279526, "dur": 128, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892279656, "dur": 7, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892279664, "dur": 127, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892279844, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892279847, "dur": 146, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892280077, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892280078, "dur": 127, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892280224, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892280266, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892280324, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892280329, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892280404, "dur": 317, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892280784, "dur": 217, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281006, "dur": 88, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281215, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281217, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281256, "dur": 85, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281343, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281384, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281420, "dur": 155, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281613, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281616, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281668, "dur": 51, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892281726, "dur": 421, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892282150, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892282154, "dur": 285, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892282457, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892282459, "dur": 107, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892282578, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892282591, "dur": 59, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892282652, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892282657, "dur": 120, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892282782, "dur": 47, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892282832, "dur": 175, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283010, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283031, "dur": 71, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283105, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283106, "dur": 92, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283200, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283202, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283294, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283296, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283344, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283346, "dur": 86, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283435, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283440, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283489, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283552, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283555, "dur": 67, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283637, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283702, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283750, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283815, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283817, "dur": 78, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283898, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283901, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283969, "dur": 6, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892283984, "dur": 96, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284084, "dur": 135, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284221, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284225, "dur": 85, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284311, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284313, "dur": 111, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284427, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284429, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284546, "dur": 100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284648, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284650, "dur": 75, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284735, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284867, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284870, "dur": 92, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284963, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892284966, "dur": 90, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285059, "dur": 72, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285133, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285216, "dur": 305, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285524, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285526, "dur": 97, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285635, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285638, "dur": 67, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285708, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285711, "dur": 144, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285859, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285873, "dur": 44, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285919, "dur": 8, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285929, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285981, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892285984, "dur": 78, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892286064, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892286150, "dur": 163, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892286316, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892286323, "dur": 189, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892286516, "dur": 1953, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892288515, "dur": 814, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892289332, "dur": 30654, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892319992, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892319997, "dur": 261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892320262, "dur": 13, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892320278, "dur": 3020, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892323302, "dur": 241, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892323547, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892323549, "dur": 761, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892324314, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892324318, "dur": 6168, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892330497, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892330499, "dur": 38873, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892369381, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892369385, "dur": 567, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892369958, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892369960, "dur": 176, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892370141, "dur": 168, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892370312, "dur": 153, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892370467, "dur": 243, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892370714, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892370856, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892370858, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892370911, "dur": 220, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892371132, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892371134, "dur": 182, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892371320, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892371490, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892371621, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892371782, "dur": 160, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892371944, "dur": 198, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892372145, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892372289, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892372345, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892372460, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892372495, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892372574, "dur": 206, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892372781, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892372886, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892372933, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892372990, "dur": 273, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892373266, "dur": 245, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892373513, "dur": 226, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892373741, "dur": 164, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892373909, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892373911, "dur": 312, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892374226, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892374354, "dur": 770, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892375133, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892375139, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892375309, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892375414, "dur": 164, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892375581, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892375760, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892375801, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892375826, "dur": 225, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892376053, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892376192, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892376311, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892376429, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892376526, "dur": 201, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892376729, "dur": 69, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892376800, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892377011, "dur": 262, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892377276, "dur": 213, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892377490, "dur": 6, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892377497, "dur": 59, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892377558, "dur": 342, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892377901, "dur": 1, "ph": "X", "name": "ProcessMessages 31", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892377945, "dur": 151, "ph": "X", "name": "ReadAsync 31", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892378097, "dur": 2, "ph": "X", "name": "ProcessMessages 17", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892378100, "dur": 96, "ph": "X", "name": "ReadAsync 17", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892378198, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892378373, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892378492, "dur": 551, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892379046, "dur": 362, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892379411, "dur": 226, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892379640, "dur": 212, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892379854, "dur": 543, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892380399, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892380517, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892380562, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892380564, "dur": 522, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892381089, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892381252, "dur": 92, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892381347, "dur": 181, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892381532, "dur": 267, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892381807, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892381855, "dur": 135, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892381992, "dur": 598, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892382593, "dur": 491, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892383087, "dur": 309, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892383399, "dur": 163, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892383565, "dur": 192, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892383760, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892383865, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892383867, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892383936, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892384035, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892384093, "dur": 37, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892384132, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892384210, "dur": 249, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892384462, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892384605, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892384638, "dur": 137, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892384778, "dur": 114, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892384895, "dur": 437, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892385334, "dur": 236, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892385586, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892385588, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892385657, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892385811, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892385919, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892385959, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892385999, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892386033, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892386200, "dur": 198, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892386401, "dur": 207, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892386611, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892386613, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892386745, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892386828, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892386937, "dur": 347, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892387287, "dur": 473, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892387762, "dur": 213, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892387979, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892387981, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892388027, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892388129, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892388172, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892388358, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892388452, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892388630, "dur": 319, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892388953, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892389049, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892389086, "dur": 230, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892389318, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892389362, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892389477, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892389666, "dur": 152, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892389820, "dur": 328, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892390150, "dur": 11, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892390164, "dur": 113521, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892503692, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892503695, "dur": 77, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892503775, "dur": 78, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892503855, "dur": 133, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892503991, "dur": 78, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892504075, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892504077, "dur": 136, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892504222, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892504227, "dur": 228, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892504457, "dur": 25, "ph": "X", "name": "ProcessMessages 2005", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892504484, "dur": 6113, "ph": "X", "name": "ReadAsync 2005", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892510602, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892510605, "dur": 174, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892510782, "dur": 475, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892511260, "dur": 1272, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892512536, "dur": 1959, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892514507, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892514509, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892514669, "dur": 725, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892515399, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892515402, "dur": 438, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892515916, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892515919, "dur": 1576, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892517499, "dur": 774, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892518276, "dur": 1387, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892519665, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892519667, "dur": 711, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892520380, "dur": 312, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892520695, "dur": 1409, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892522113, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892522114, "dur": 509, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892522628, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892522637, "dur": 1117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892523758, "dur": 2164, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892525927, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892525928, "dur": 406, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892526343, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892526347, "dur": 1501, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892527852, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892527854, "dur": 166, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892528026, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892528027, "dur": 267, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892528297, "dur": 1274, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892529573, "dur": 79, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892529653, "dur": 68, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892529723, "dur": 96, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892529822, "dur": 350, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892530173, "dur": 15, "ph": "X", "name": "ProcessMessages 3805", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892530189, "dur": 4442, "ph": "X", "name": "ReadAsync 3805", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892534634, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892534636, "dur": 1945, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892536583, "dur": 3805, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892540392, "dur": 1701, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892542096, "dur": 5785, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892547886, "dur": 4237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892552126, "dur": 1668, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892553812, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892553814, "dur": 326, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892554148, "dur": 362, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892554512, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892554515, "dur": 3250, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892557768, "dur": 1217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892558989, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892559054, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892559055, "dur": 755, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892559813, "dur": 2165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892561982, "dur": 780, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892562764, "dur": 1110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892563877, "dur": 10, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892563888, "dur": 790, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892564682, "dur": 2586, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892567272, "dur": 196, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892567476, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892567478, "dur": 136, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892567616, "dur": 183, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892567830, "dur": 103, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892567936, "dur": 54, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892567993, "dur": 127, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892568123, "dur": 132, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892568258, "dur": 168, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892568429, "dur": 94, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892568527, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892568721, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892568836, "dur": 53, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892568891, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569034, "dur": 112, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569148, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569267, "dur": 115, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569384, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569420, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569519, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569577, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569703, "dur": 134, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569840, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569931, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892569966, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570003, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570066, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570103, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570139, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570142, "dur": 48, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570192, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570243, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570288, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570367, "dur": 113, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570483, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570525, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570567, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570651, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570733, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570779, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570856, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570902, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892570978, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571050, "dur": 101, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571154, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571192, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571195, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571299, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571351, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571393, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571396, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571478, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571595, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571659, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571703, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892571790, "dur": 267, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892572060, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892572151, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892572340, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892572399, "dur": 412, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892572814, "dur": 404, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892573220, "dur": 338, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892573561, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892573563, "dur": 125, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892573698, "dur": 1230, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892574934, "dur": 1, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 30682, "tid": 34359738368, "ts": 1751677892574936, "dur": 3418, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 30682, "tid": 26, "ts": 1751677892579153, "dur": 1219, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 30682, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 30682, "tid": 30064771072, "ts": 1751677892239600, "dur": 85025, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 30682, "tid": 30064771072, "ts": 1751677892324626, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 30682, "tid": 30064771072, "ts": 1751677892324632, "dur": 164, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 30682, "tid": 26, "ts": 1751677892580380, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 30682, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 30682, "tid": 25769803776, "ts": 1751677892219276, "dur": 359361, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 30682, "tid": 25769803776, "ts": 1751677892219575, "dur": 19914, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 30682, "tid": 25769803776, "ts": 1751677892578644, "dur": 66, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 30682, "tid": 25769803776, "ts": 1751677892578652, "dur": 30, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 30682, "tid": 26, "ts": 1751677892580388, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751677892251165, "dur": 2153, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751677892253324, "dur": 1088, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751677892254466, "dur": 98, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751677892254565, "dur": 141, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751677892254772, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1751677892254917, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BC34EE124D99405F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751677892256322, "dur": 933, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_F93EF442EF84B168.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751677892257667, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751677892262358, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751677892263419, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_AAF6E30B590FA4FC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751677892263886, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751677892265161, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TerrainTools.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751677892266718, "dur": 201, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751677892266945, "dur": 149, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751677892267281, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Autodesk.Fbx.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751677892269818, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751677892254766, "dur": 20501, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751677892275277, "dur": 299035, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751677892575357, "dur": 790, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751677892254606, "dur": 20691, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892275364, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0605B85485502BE7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892275575, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892275754, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BEA590CADB427334.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892275904, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892276035, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_7BDBB2C42302BC9A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892276265, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892276461, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3C49F49827521165.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892276684, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_4B23DE3D0EC5E9F6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892276925, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892277051, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_FDCB5615933DABBC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892277355, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892277488, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_5FA3D4B57F988593.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892277699, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892277902, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_9BDBADA58B2ADD51.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892278093, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892278231, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_5B8A9BD2EFA46D52.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892278516, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892278653, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_DC7A7F136F4CE4C2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892278930, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892279054, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_EB75E7D5F6BAF9EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892279280, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892279414, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_AD7AB54F961F77D0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892279646, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892279810, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892280024, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892280200, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_5C7E66637B1B7A5F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892280433, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892280560, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892280770, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892280932, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_A2EC96C261859A16.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892281239, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892281373, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892281558, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892281764, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751677892281819, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892281995, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892282149, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892282268, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892282462, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892282751, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892282955, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892283111, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892283317, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892283468, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892283599, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892283731, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892283891, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892284033, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892284153, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892284291, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892284435, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892284596, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892284748, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892284889, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892285102, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892285264, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892285503, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892285713, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892285916, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892286141, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892286293, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892286444, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892286580, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892286722, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892286927, "dur": 3114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892290042, "dur": 2618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892292660, "dur": 2405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892295066, "dur": 2182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892297248, "dur": 2382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892299630, "dur": 2334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892301964, "dur": 2038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892304002, "dur": 2426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892306429, "dur": 2412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892308841, "dur": 2474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892311315, "dur": 2695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892314010, "dur": 2323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892316334, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892317276, "dur": 2405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892319681, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892321062, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892322618, "dur": 2640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892325258, "dur": 2620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892327879, "dur": 2370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892330250, "dur": 2674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892332925, "dur": 2655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892335580, "dur": 2342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892337922, "dur": 3136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892341059, "dur": 2931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892343991, "dur": 2942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892346934, "dur": 3098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892350032, "dur": 3036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892353068, "dur": 2614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892355683, "dur": 2577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892358261, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892359749, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892360935, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892362278, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892363606, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892365032, "dur": 1473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892366505, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892367529, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/TestRunner/Utils/TestListCache.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751677892367529, "dur": 2043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892369693, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892369904, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892370479, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892370823, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.Stl.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892371406, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892371472, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Autodesk.Fbx.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892371731, "dur": 5030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.Csg.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892376765, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892376860, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ProBuilder.Csg.ref.dll_DECA642FAFEF16A9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892376949, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892377045, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892377340, "dur": 1718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892379059, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892379444, "dur": 1593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TerrainTools.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892381037, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892381139, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892381897, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892382000, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892382327, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892383209, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892383309, "dur": 2111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Polybrush.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892385421, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892385556, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892386310, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892386546, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892386642, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892387181, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892387425, "dur": 1477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892388927, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892389026, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892389513, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892389638, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892389955, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892390019, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892390237, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751677892390379, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892528653, "dur": 1816, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892390721, "dur": 139970, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892564704, "dur": 6087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProBuilder.AddOns.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751677892570792, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892570922, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.AddOns.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751677892571076, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892571195, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892571319, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892571435, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892571569, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892571653, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Formats.Fbx.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751677892571705, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892571824, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892571953, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892572141, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892572253, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892572309, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751677892572881, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751677892572959, "dur": 1291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892254615, "dur": 20702, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892275375, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0C77AB8E2EE782AD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892275579, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892275760, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_9D7940B00A5C3BC2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892275905, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892276048, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_E899CA1EE3F604B7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892276251, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892276398, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_ADE8C2B6B820D212.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892276664, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892276783, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_9E91C57C5593A91B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892277013, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892277158, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_D4FE6C53408DDD32.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892277454, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892277591, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_96079F232EEC93D6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892277871, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892277987, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_79EE1BD87F9D76E8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892278156, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892278371, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_2CF8765FBD248A7A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892278567, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892278821, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_C588BC933FA70ECF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892278987, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892279132, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5B0232F48FD91F71.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892279380, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892279561, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892279705, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_550876C417D13C9A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892279912, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892280056, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892280252, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892280441, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892280587, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892280812, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892280982, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_5868DE08B6C73CB9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892281234, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892281346, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892281559, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892281825, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892281998, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892282152, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892282297, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892282437, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892282717, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892282930, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892283100, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892283320, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892283497, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892283633, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892283763, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892283943, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892284083, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892284215, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892284347, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892284503, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892284654, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892284834, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892285014, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892285190, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892285444, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892285652, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892285864, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892286034, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892286206, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892286378, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892286507, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892286624, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892286830, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892286994, "dur": 3012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892290006, "dur": 2625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892292631, "dur": 2389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892295020, "dur": 2180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892297200, "dur": 2375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892299576, "dur": 2296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892301873, "dur": 1832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892303705, "dur": 2470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892306175, "dur": 2417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892308592, "dur": 2319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892310912, "dur": 2791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892313704, "dur": 2549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892316253, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892317197, "dur": 2196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892319394, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892320672, "dur": 1719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892322391, "dur": 2334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892324757, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892324878, "dur": 2728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892327607, "dur": 2439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892330047, "dur": 2602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892332649, "dur": 2587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892335236, "dur": 2497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892337733, "dur": 2925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892340658, "dur": 2966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892343624, "dur": 2969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892346593, "dur": 3010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892349603, "dur": 3001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892352604, "dur": 2672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892355277, "dur": 2694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892357972, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892359477, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892360704, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892362088, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892363395, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892364679, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892367522, "dur": 602, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.ugui@ae559ff5fff9/Editor/UGUI/UI/GridLayoutGroupEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751677892366273, "dur": 2371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892368644, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892369382, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892369695, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892369918, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892370473, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892371084, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892371903, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892372036, "dur": 2702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Autodesk.Fbx.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892374742, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892374863, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892375840, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892376128, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892377117, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892377173, "dur": 1601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892378775, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892378990, "dur": 1228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892380268, "dur": 2899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892383168, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892383336, "dur": 3444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892386781, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892386878, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892387875, "dur": 1650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892389526, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751677892389651, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892389899, "dur": 117549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892507449, "dur": 3650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892511100, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892511221, "dur": 6413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892517634, "dur": 627, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892518268, "dur": 2977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892521245, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892521382, "dur": 6697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892528080, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892528161, "dur": 8759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProBuilder.AssetIdRemapUtility.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892536921, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892537077, "dur": 17146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892554224, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892554350, "dur": 8688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892563038, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892563294, "dur": 4795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751677892568090, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892568212, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892568285, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751677892568341, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892568463, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892568653, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892568767, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892568887, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751677892568945, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892569061, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892569135, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Cinemachine.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751677892569195, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892569356, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751677892569412, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892569570, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892569692, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892569908, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892569998, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892570171, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.unity.cinemachine.editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751677892570236, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892570390, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751677892570458, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892570583, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751677892570641, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892570888, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892570995, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892571058, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892571144, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892571256, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892571367, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892571468, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892571653, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892571777, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751677892571832, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892571970, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TerrainTools.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751677892572030, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892572185, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892572280, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751677892572343, "dur": 1429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751677892573772, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892254623, "dur": 20702, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892275328, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_60AB0A8C19591804.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892275570, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892275770, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_BFEA7F72747E8B5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892275909, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892276053, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_033FA273D52030EB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892276257, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892276432, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B50BAC1929889709.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892276670, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892276823, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D00F03FB1AB43583.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892277028, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892277185, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_E0709E0C6D1E6C5A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892277454, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892277580, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_935DAD7C03D7406B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892277845, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892277947, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_9610079563F7DD27.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892278150, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892278347, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_F50A132300C347E2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892278557, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892278759, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_02E8179AAFC35926.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892278966, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892279083, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_F6C95917098D0BB0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892279305, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892279519, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_DC30A64E8A644388.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892279706, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892279855, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892280088, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892280251, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892280415, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892280569, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892280781, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892280953, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A1148DEAE75EA1F6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892281232, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892281339, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892281481, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892281716, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892281900, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892282031, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892282206, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892282388, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892282545, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892282840, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892283066, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892283237, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892283426, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892283557, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892283707, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892283861, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892284004, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892284111, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892284236, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892284368, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892284512, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892284650, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751677892284718, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892284837, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892285008, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892285174, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892285399, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892285579, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892285779, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892285970, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892286114, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892286269, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892286463, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892286615, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892286754, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892286959, "dur": 2982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892289942, "dur": 2675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892292617, "dur": 2338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892294956, "dur": 2221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892297178, "dur": 2409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892299587, "dur": 2299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892301886, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892303770, "dur": 2462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892306233, "dur": 2409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892308642, "dur": 2419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892311062, "dur": 2806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892313870, "dur": 2438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892316308, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892317238, "dur": 2325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892319563, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892320906, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892322519, "dur": 2395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892324914, "dur": 2808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892327723, "dur": 2399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892330122, "dur": 2681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892332803, "dur": 2724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892335527, "dur": 2330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892337858, "dur": 2874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892340732, "dur": 2982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892343714, "dur": 2958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892346673, "dur": 2994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892349667, "dur": 2987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892352655, "dur": 2656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892355311, "dur": 2694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892358005, "dur": 1533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892359539, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892360765, "dur": 1364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892362129, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892363437, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892364708, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892366359, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892367531, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/UnityTestProtocol/UtpMessageBuilder.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751677892367158, "dur": 2064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892369223, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892369440, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892369649, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892369715, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892369868, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892370324, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892370762, "dur": 1424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751677892372186, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892372403, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892372554, "dur": 1269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751677892373823, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892373957, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751677892374136, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751677892375087, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892375291, "dur": 1818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1751677892377247, "dur": 522, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892504164, "dur": 632, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892378018, "dur": 126793, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1751677892506048, "dur": 5797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Autodesk.Fbx.BuildTestAssets.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751677892511846, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892511930, "dur": 3975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751677892515907, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892516101, "dur": 8090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751677892524192, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892524448, "dur": 3980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Polybrush.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751677892528429, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892528538, "dur": 19538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751677892548077, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892548276, "dur": 11195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751677892559471, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751677892559615, "dur": 13709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751677892573401, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892254631, "dur": 20706, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892275340, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BC34EE124D99405F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892275647, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892275786, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_DCDFD5108FAD48E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892275897, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892275959, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_980FF8A23A8B25EC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892276208, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892276316, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_56BC239FF7D765B9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892276608, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892276723, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_D5F2AFDE10CF03FC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892276955, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892277094, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_691063613D964F7A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892277410, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892277498, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_AF5B257B286ED30B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892277764, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892277894, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A92E4FC8FAF84906.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892278084, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892278197, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_026D6EC796805E99.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892278483, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892278614, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_EE5F99ADF630153C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892278908, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892279032, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_5B520DDFB3F6DA1B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892279261, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892279400, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_D386EFB1760F37EA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892279637, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892279775, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892280012, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892280163, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892280293, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892280468, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_2FAFF9F1339AD54A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892280739, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892280942, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892281122, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892281272, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892281458, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892281665, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892281875, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892282098, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892282239, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892282404, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892282554, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892282852, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892283045, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892283211, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892283421, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892283538, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892283690, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892283859, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892283997, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892284125, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892284257, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892284374, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892284518, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892284708, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892284841, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892285018, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892285194, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892285411, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892285625, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892285798, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892285994, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892286181, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892286345, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892286479, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892286617, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892286749, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892286939, "dur": 3113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892290052, "dur": 2623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892292675, "dur": 2377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892295052, "dur": 2162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892297214, "dur": 2424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892299638, "dur": 2333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892301972, "dur": 2049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892304021, "dur": 2372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892306393, "dur": 2367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892308761, "dur": 2396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892311158, "dur": 2834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892313992, "dur": 2344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892316336, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892317255, "dur": 2318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892319573, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892320930, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892322531, "dur": 2371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892324903, "dur": 2708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892327611, "dur": 2336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892329947, "dur": 2665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892332612, "dur": 2568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892335180, "dur": 2394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892337575, "dur": 2858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892340433, "dur": 3048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892343481, "dur": 2859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892346341, "dur": 3092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892349433, "dur": 2896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892352329, "dur": 2648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892354977, "dur": 2869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892357846, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892359316, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892360595, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892361987, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892363301, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892364585, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892366127, "dur": 2350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892368477, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892369190, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892369629, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892369721, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892369872, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892370336, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.KdTree.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892370617, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.KdTree.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892371481, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892371616, "dur": 1934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.Stl.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892373550, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892373692, "dur": 2476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Polybrush.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892376170, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892376385, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892376673, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Polybrush.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892377271, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892377502, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892377765, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892377925, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892378175, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892379052, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892379186, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892379606, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892379844, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892382563, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892382680, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892383536, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892383746, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892383868, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892384452, "dur": 1646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Autodesk.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892386098, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892386200, "dur": 1104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892387304, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892387378, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892387876, "dur": 2364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892390241, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751677892390402, "dur": 117282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892507684, "dur": 13150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Polybrush.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892520834, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892521000, "dur": 3188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProBuilder.Poly2Tri.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892524190, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892524348, "dur": 10535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TerrainTools.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892534883, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892535066, "dur": 24227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Formats.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892559294, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751677892559434, "dur": 14306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751677892573769, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892254638, "dur": 20704, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892275344, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_3C830162D7931A68.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892275597, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892275767, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_14C64E7C47C0B0E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892275916, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892276060, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_B7BFBC1EE01A84B0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892276261, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892276449, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_2E3030C5EEAB0844.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892276677, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892276839, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_D865091824167876.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892277038, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892277215, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_DB818D17EFC4CFE4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892277463, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892277597, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_399D5415CA475E06.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892277879, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892278004, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_7D670AE824B60A84.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892278177, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892278405, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_1FECEC7B0258D8A1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892278585, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892278845, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_BD153B684B41233F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892279030, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892279158, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_70CE620B8383EC5F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892279374, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892279541, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892279672, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_97F68DE83DFCC830.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892279885, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892280031, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892280208, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892280370, "dur": 9321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892289691, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892289829, "dur": 30472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892320302, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892320493, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892320584, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892320774, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892322500, "dur": 2293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892324794, "dur": 2739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892327533, "dur": 2583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892330116, "dur": 2678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892332795, "dur": 2564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892335360, "dur": 2400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892337760, "dur": 2908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892340669, "dur": 2948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892343617, "dur": 2952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892346569, "dur": 3044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892349613, "dur": 3030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892352643, "dur": 2645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892355289, "dur": 2688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892357978, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892359485, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892360724, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892362098, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892363405, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892364688, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892366356, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892367618, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/TestRun/Tasks/RestoreProjectSettingsTask.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751677892367618, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892369251, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892369336, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892369552, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892369712, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892369892, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892370469, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892370925, "dur": 1008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892371934, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892372157, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892372633, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892372907, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TerrainTools.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892373271, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892373502, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892374476, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892374582, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TerrainTools.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892375695, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892375816, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892376376, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892376447, "dur": 3521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892379969, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892380168, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892380924, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892381031, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892381644, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892381783, "dur": 2979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892384763, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892384948, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892385222, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892385388, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Autodesk.Fbx.BuildTestAssets.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892386232, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892386416, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892386508, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892386675, "dur": 1105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892387780, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751677892387908, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892388537, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892388608, "dur": 117449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892506062, "dur": 9095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProBuilder.Stl.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892515158, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892515303, "dur": 3544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892518847, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892519122, "dur": 3965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892523089, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892523219, "dur": 3900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892527120, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892527275, "dur": 13355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892540631, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892540810, "dur": 17277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892558088, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892558261, "dur": 10061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProBuilder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751677892568323, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892568438, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892568631, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751677892568683, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892568795, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892568896, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892569022, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751677892569104, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892569234, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892569306, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.Stl.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751677892569362, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892569481, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751677892569535, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892569659, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751677892569725, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892569825, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892569964, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892570155, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892570307, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892570579, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751677892570638, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892570736, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892570945, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProBuilder.AddOns.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751677892571005, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892571116, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892571232, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892571338, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892571539, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892571626, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Polybrush.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751677892571728, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751677892571801, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892571938, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892572036, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892572215, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892572332, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751677892573381, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892254646, "dur": 20707, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892275356, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2A3CED83994B17D9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892275570, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892275741, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_4EE0B950E71B5A25.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892275903, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892276005, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_C4F4BA60DADEE500.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892276241, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892276385, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_114BF97CB5CCE0C4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892276664, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892276777, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_58E849BC49E0C671.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892277013, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892277121, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_B73E1B18F05B0A85.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892277436, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892277537, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_3F1BF2668D5B0F92.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892277856, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892277963, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_66438771D87F9323.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892278144, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892278266, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_8FF3F95A6F6B2307.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892278537, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892278693, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_E737866DF38EBDF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892278961, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892279076, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_F45CED2A202838E9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892279309, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892279425, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_6C3E58AF11584EFF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892279658, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892279827, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892280037, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892280216, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892280386, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892280553, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892280764, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892280945, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_D4B653123EE78879.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892281228, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892281345, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892281512, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892281741, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892281961, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892282123, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892282257, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892282415, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892282561, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751677892282634, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892282873, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892283075, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892283251, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892283443, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892283587, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892283718, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892283880, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892284009, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892284144, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892284264, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892284388, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892284541, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892284741, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892284881, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892285089, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892285305, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892285528, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892285754, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892285957, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892286122, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892286278, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892286422, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892286564, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892286713, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892286891, "dur": 3134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892290025, "dur": 2600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892292625, "dur": 2364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892294989, "dur": 2179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892297168, "dur": 2397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892299566, "dur": 2302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892301868, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892303696, "dur": 2475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892306171, "dur": 2406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892308578, "dur": 2336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892310915, "dur": 2764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892313679, "dur": 2578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892316257, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892317199, "dur": 2270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892319470, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892320836, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892322466, "dur": 2320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892324787, "dur": 2765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892327553, "dur": 2446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892329999, "dur": 2598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892332597, "dur": 2610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892335207, "dur": 2438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892337645, "dur": 2877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892340523, "dur": 3019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892343543, "dur": 2911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892346455, "dur": 3067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892349522, "dur": 2971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892352493, "dur": 2637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892355131, "dur": 2750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892357881, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892359433, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892360686, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892362071, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892363388, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892364675, "dur": 1577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892366252, "dur": 2349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892368602, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892369301, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892369723, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892369873, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892370327, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892370601, "dur": 2357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892372958, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892373113, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892373398, "dur": 6199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892379601, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892379791, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892380012, "dur": 1127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892381173, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Formats.Fbx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892381979, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892382210, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Formats.Fbx.Editor.ref.dll_3576B7E14BA3FAF3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892382393, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892382480, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892382762, "dur": 1340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892384104, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892384286, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892384815, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892384910, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892384964, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Autodesk.Fbx.BuildTestAssets.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892385249, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892385360, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892385417, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892386211, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892386408, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892386468, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892386615, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892386690, "dur": 1125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892387817, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751677892387969, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892388682, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892388748, "dur": 117307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892506056, "dur": 5255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProBuilder.Csg.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892511312, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892511411, "dur": 3612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892515023, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892515155, "dur": 8257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProBuilder.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892523412, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892523520, "dur": 2950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892526471, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892526653, "dur": 15800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892542453, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892542546, "dur": 21660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892564207, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751677892564352, "dur": 9801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751677892574188, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892254655, "dur": 20721, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892275380, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_C391B64780FF4489.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892275606, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892275773, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_91153961DE272840.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892275913, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892276112, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_472941402CA95004.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892276280, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892276482, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_8D95A07507699339.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892276689, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892276852, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_40614C3DAEFC5073.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892277058, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892277262, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2FE9AFF50906255D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892277481, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892277629, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_3D14F0A397ED1B8A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892277872, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892277999, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_19CE576A8740C2DD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892278165, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892278365, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_93D5C95D9409A34A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892278560, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892278770, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_7DA08621B02355B1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892278983, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892279118, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_49449C1138C4FDC9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892279336, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892279532, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892279673, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892279809, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892280017, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892280188, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_5CC24A7A8CC65EC8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892280392, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892280575, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892280829, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892280990, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_B867065A6613A379.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892281248, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892281431, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892281607, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892281840, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892282047, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892282218, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892282350, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751677892282419, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892282644, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892282907, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892283087, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892283259, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892283451, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892283574, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892283714, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892283905, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892284061, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892284194, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892284320, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892284457, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892284619, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892284812, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892284959, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892285151, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892285387, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892285563, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892285773, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892285971, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892286163, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892286311, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892286428, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892286549, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892286707, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892286889, "dur": 3121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892290010, "dur": 2590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892292600, "dur": 2404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892295004, "dur": 2204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892297209, "dur": 1933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892299143, "dur": 2415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892301559, "dur": 1851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892303410, "dur": 2466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892305877, "dur": 2544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892308422, "dur": 2399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892310822, "dur": 2831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892313655, "dur": 2563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892316218, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892317180, "dur": 2144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892319325, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892320659, "dur": 1722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892322381, "dur": 2326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892324731, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892324809, "dur": 2793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892327603, "dur": 2405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892330008, "dur": 2563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892332571, "dur": 2583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892335155, "dur": 2400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892337555, "dur": 2900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892340456, "dur": 3076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892343532, "dur": 2851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892346384, "dur": 3091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892349475, "dur": 2917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892352393, "dur": 2678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892355071, "dur": 2824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892357895, "dur": 1542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892359438, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892360673, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892362061, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892363368, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892364664, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892367512, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.burst@616862665d8c/Runtime/CompilerServices/Loop.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751677892366200, "dur": 2391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892368591, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892369292, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892369703, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892369898, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892370470, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892370898, "dur": 1280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892372179, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892372340, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Polybrush.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892373312, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892373544, "dur": 1337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892374884, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892375007, "dur": 1154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892376162, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892376306, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D99F3560B5399C1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892376415, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892376638, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892377094, "dur": 993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TerrainTools.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892378118, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892380404, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892380511, "dur": 1548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892382059, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892382228, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751677892382446, "dur": 4725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892387171, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892387282, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892387876, "dur": 120185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892508061, "dur": 3456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProBuilder.KdTree.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892511518, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892511620, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TerrainTools.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892514406, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892514469, "dur": 3589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892518059, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892518220, "dur": 4076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892522297, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892522426, "dur": 7738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892530176, "dur": 573, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892530751, "dur": 23749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892554500, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892554660, "dur": 5569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892560229, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892560306, "dur": 7445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751677892567752, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892567950, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892568130, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751677892568188, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892568458, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892568617, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892568806, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892569022, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892569188, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751677892569254, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892569445, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751677892569498, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892569556, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892569663, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892569799, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Autodesk.Fbx.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751677892569850, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892569956, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751677892570019, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892570142, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892570223, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892570292, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892570348, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751677892570398, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892570555, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892570679, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751677892570741, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892570907, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892571066, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892571176, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892571282, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892571355, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892571439, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892571576, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892571694, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892571783, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892571900, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892572031, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751677892572113, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892572296, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751677892572691, "dur": 1490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892254668, "dur": 20721, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892275392, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_046D4E7C1D7E5FB1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892275642, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892275736, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_19F41D50EFE7B05F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892275943, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_E73127057FEBEEC1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892276161, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892276281, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_30D44940D248A01A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892276547, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892276697, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_7ECFDDCD1A79843E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892276921, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892277043, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_19D6929C8062165E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892277314, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892277477, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_6E14C5940F10851F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892277681, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892277872, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_90F12F76EF0F6F54.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892278057, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892278169, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_54A688DFF9556AAA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892278476, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892278580, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_7C8A200C2546D5E5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892278885, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892279002, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_4936DC4A5CE64CFE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892279221, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892279378, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_2595105C60E6BB26.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892279610, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892279754, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_61E9F21A24B51F63.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892279977, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892280110, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892280266, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892280447, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892280624, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892280847, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892281008, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892281228, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892281321, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892281473, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892281694, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892281868, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892282065, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892282248, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892282401, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892282540, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892282825, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892283040, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892283188, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892283402, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892283524, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892283665, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892283816, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892283979, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892284134, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892284271, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892284409, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892284563, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892284741, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892284874, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892285040, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892285199, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892285423, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892285620, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892285810, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892285988, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892286154, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892286307, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892286434, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892286556, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892286690, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892286871, "dur": 3049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892289921, "dur": 2648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892292570, "dur": 2337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892294907, "dur": 2240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892297148, "dur": 2371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892299519, "dur": 2347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892301866, "dur": 1895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892303761, "dur": 2468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892306229, "dur": 2403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892308633, "dur": 2374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892311008, "dur": 2827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892313835, "dur": 2441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892316276, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892317215, "dur": 2296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892319511, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892320888, "dur": 1620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892322509, "dur": 2324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892324834, "dur": 2803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892327637, "dur": 2524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892330162, "dur": 2682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892332844, "dur": 2590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892335434, "dur": 2378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892337813, "dur": 2886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892340700, "dur": 3004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892343704, "dur": 2914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892346619, "dur": 3040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892349660, "dur": 3042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892352702, "dur": 2714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892355416, "dur": 2615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892358031, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892359581, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892360791, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892362186, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892363465, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892364731, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892366384, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892367500, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/UnityTestProtocol/Messages/BuildSettingsMessage.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751677892367184, "dur": 2205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892369389, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892369725, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892369874, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892370340, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892370556, "dur": 2116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.Poly2Tri.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892372672, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892372823, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892373180, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProBuilder.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892374282, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892374449, "dur": 2385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892376834, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892377017, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892377412, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892377638, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892377876, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892378069, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892378258, "dur": 3407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892381665, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892381883, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892382529, "dur": 1405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892383934, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892384085, "dur": 1824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892385909, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892386081, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751677892386542, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892386615, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892387409, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892387480, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892387877, "dur": 118171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892506062, "dur": 3312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892509374, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892509495, "dur": 3620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892513115, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892513197, "dur": 2728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892515926, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892516132, "dur": 12682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892528815, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892528898, "dur": 1808, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892530708, "dur": 23410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892554118, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892554307, "dur": 10853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892565161, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892565257, "dur": 7306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751677892572645, "dur": 1461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751677892574140, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892254697, "dur": 20730, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892275427, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_30A98725FD4814A1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892275643, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892275730, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_0FBE9ED608B4E8D6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892275919, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892276066, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_FF410ACE9D2596E6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892276258, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892276415, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_EAC77A8DF476E0A4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892276664, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892276805, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_5BE518023E3CFD1E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892277021, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892277173, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_7988411193018D76.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892277454, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892277563, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_DD47F8FA85D8F191.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892277860, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892277978, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_DFFE08C20939B21A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892278147, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892278319, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_C098ECBA76C2A4A7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892278556, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892278731, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_AA3A74C945296BF5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892278980, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892279100, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_220CDF9142A7B095.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892279323, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892279491, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_904B01EF2F66CB3B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892279700, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892279867, "dur": 8904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892288772, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892288922, "dur": 34409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892323331, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892323714, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892323885, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892324056, "dur": 6687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892330744, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892330888, "dur": 38918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892369888, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892369970, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892370309, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892370497, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892371265, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892371399, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892371753, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892372923, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892373027, "dur": 1042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892374091, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892374275, "dur": 4300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892378580, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892378895, "dur": 1343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892380238, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892380349, "dur": 1291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892381641, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892381854, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892382418, "dur": 2626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892385044, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892385163, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892385381, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892387777, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751677892387889, "dur": 1663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892389615, "dur": 116856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892506471, "dur": 9889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892516361, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892516478, "dur": 3729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Formats.Fbx.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892520208, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892520356, "dur": 5687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892526044, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892526127, "dur": 4206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892530333, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892530397, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892530773, "dur": 21649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892552423, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892552637, "dur": 9693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892562330, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892562467, "dur": 4873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892567341, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751677892567417, "dur": 6648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751677892574112, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892254710, "dur": 20709, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892275420, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_57990D93C36F600B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892275625, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892275704, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5960159A456858DE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892275873, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892275990, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_2610F748A29B6B4F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892276236, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892276357, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_9A2EF6715F58AFA7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892276646, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892276737, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_4C111AC8913F36E0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892276963, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892277103, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_27E591C31FC52D84.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892277423, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892277514, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D7F6979113782070.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892277827, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892277930, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_B1E7E07859332157.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892278132, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892278245, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_748B81B3C0C22519.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892278531, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892278684, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B925935CBA9FE2D0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892278956, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892279067, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_F93EF442EF84B168.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892279311, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892279502, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4FB991108DA1F00B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892279673, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892279819, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892280014, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892280181, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892280330, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892280494, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892280726, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892280882, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892281061, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892281257, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892281447, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892281628, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892281831, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892282025, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892282170, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892282316, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892282505, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892282800, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892283015, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892283156, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892283370, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892283521, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892283656, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892283838, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892284017, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892284174, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892284303, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892284428, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892284549, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892284731, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892284862, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892285047, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892285202, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892285484, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892285671, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892285852, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892286008, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892286177, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892286352, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892286529, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892286668, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892286852, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892287026, "dur": 2996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892290022, "dur": 2611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892292634, "dur": 2362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892294997, "dur": 2190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892297187, "dur": 2382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892299569, "dur": 2314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892301884, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892303737, "dur": 2455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892306192, "dur": 2453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892308645, "dur": 2345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892310991, "dur": 2794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892313785, "dur": 2483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892316268, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892317208, "dur": 2275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892319483, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892320824, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892322451, "dur": 2340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892324791, "dur": 2729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892327890, "dur": 396, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.0a4/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 10, "ts": 1751677892328286, "dur": 1424, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.0a4/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 10, "ts": 1751677892329711, "dur": 790, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.0a4/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 10, "ts": 1751677892327521, "dur": 2981, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892330502, "dur": 2574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892333076, "dur": 2612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892335688, "dur": 2364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892338052, "dur": 3132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892341184, "dur": 2883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892344067, "dur": 2979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892347047, "dur": 3062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892350109, "dur": 3044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892353153, "dur": 2574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892355727, "dur": 2549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892358276, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892359751, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892360944, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892362284, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892363629, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892365066, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892366517, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892367536, "dur": 613, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.test-framework@5ac417e07314/UnityEditor.TestRunner/TestRunner/EditModePCHelper.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751677892367536, "dur": 2004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892369540, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892369716, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892369871, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892370326, "dur": 1077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892371440, "dur": 7047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892378490, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892378606, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892378735, "dur": 1556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892380324, "dur": 6621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892387028, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892387189, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892388349, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892388501, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751677892388653, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892389169, "dur": 116905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892506075, "dur": 3007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892509082, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892509207, "dur": 3366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892512573, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892512701, "dur": 6130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892518832, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892519010, "dur": 3677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Autodesk.Fbx.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892522688, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892522786, "dur": 3988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892526775, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892526919, "dur": 3108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892530027, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892530111, "dur": 608, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892530721, "dur": 24159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892554881, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892555027, "dur": 9927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Autodesk.Fbx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892564955, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751677892565053, "dur": 7795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751677892572904, "dur": 1338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751677892577837, "dur": 768, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 30682, "tid": 26, "ts": 1751677892580430, "dur": 9, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 30682, "tid": 26, "ts": 1751677892580468, "dur": 468, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 30682, "tid": 26, "ts": 1751677892579088, "dur": 1864, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}